const {getDefaultConfig, mergeConfig} = require('@react-native/metro-config');

/**
 * Metro configuration
 * https://facebook.github.io/metro/docs/configuration
 *
 * @type {import('metro-config').MetroConfig}
 */
const config = {
  transformer: {
    minifierConfig: {
      // Enable advanced minification for production
      mangle: {
        keep_fnames: true,
      },
      compress: {
        drop_console: process.env.NODE_ENV === 'production', // Remove console.log in production
        drop_debugger: true,
        dead_code: true,
        evaluate: true,
        conditionals: true,
        booleans: true,
        unused: true,
        if_return: true,
        join_vars: true,
        collapse_vars: true,
      },
    },
  },
  resolver: {
    alias: {
      // Alias for smaller lodash imports
      'lodash': 'lodash-es',
    },
  },
};

module.exports = mergeConfig(getDefaultConfig(__dirname), config);
