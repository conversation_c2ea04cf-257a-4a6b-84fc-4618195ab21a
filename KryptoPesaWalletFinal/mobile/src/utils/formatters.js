/**
 * Utility functions for formatting data
 */

/**
 * Format currency amount
 * @param {number} amount - The amount to format
 * @param {string} currency - Currency code (e.g., 'USD', 'KES')
 * @param {string} locale - Locale for formatting (default: 'en-US')
 * @returns {string} Formatted currency string
 */
export const formatCurrency = (amount, currency = 'USD', locale = 'en-US') => {
  if (amount === null || amount === undefined || isNaN(amount)) {
    return '0.00';
  }

  try {
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);
  } catch (error) {
    // Fallback for unsupported currencies
    return `${currency} ${parseFloat(amount).toFixed(2)}`;
  }
};

/**
 * Format cryptocurrency amount
 * @param {number} amount - The amount to format
 * @param {string} symbol - Crypto symbol (e.g., 'BTC', 'ETH')
 * @param {number} decimals - Number of decimal places (default: 8)
 * @returns {string} Formatted crypto string
 */
export const formatCrypto = (amount, symbol = 'BTC', decimals = 8) => {
  if (amount === null || amount === undefined || isNaN(amount)) {
    return `0 ${symbol}`;
  }

  const formatted = parseFloat(amount).toFixed(decimals);
  // Remove trailing zeros
  const trimmed = formatted.replace(/\.?0+$/, '');
  return `${trimmed} ${symbol}`;
};

/**
 * Format date
 * @param {Date|string|number} date - Date to format
 * @param {string} format - Format type ('short', 'long', 'time', 'datetime')
 * @param {string} locale - Locale for formatting (default: 'en-US')
 * @returns {string} Formatted date string
 */
export const formatDate = (date, format = 'short', locale = 'en-US') => {
  if (!date) return '';

  const dateObj = new Date(date);
  if (isNaN(dateObj.getTime())) return '';

  try {
    switch (format) {
      case 'short':
        return dateObj.toLocaleDateString(locale, {
          year: 'numeric',
          month: 'short',
          day: 'numeric',
        });
      
      case 'long':
        return dateObj.toLocaleDateString(locale, {
          year: 'numeric',
          month: 'long',
          day: 'numeric',
          weekday: 'long',
        });
      
      case 'time':
        return dateObj.toLocaleTimeString(locale, {
          hour: '2-digit',
          minute: '2-digit',
        });
      
      case 'datetime':
        return dateObj.toLocaleString(locale, {
          year: 'numeric',
          month: 'short',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit',
        });
      
      case 'relative':
        return formatRelativeTime(dateObj);
      
      default:
        return dateObj.toLocaleDateString(locale);
    }
  } catch (error) {
    return dateObj.toString();
  }
};

/**
 * Format relative time (e.g., "2 hours ago", "in 3 days")
 * @param {Date} date - Date to format
 * @returns {string} Relative time string
 */
export const formatRelativeTime = (date) => {
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();
  const diffSeconds = Math.floor(diffMs / 1000);
  const diffMinutes = Math.floor(diffSeconds / 60);
  const diffHours = Math.floor(diffMinutes / 60);
  const diffDays = Math.floor(diffHours / 24);

  if (Math.abs(diffSeconds) < 60) {
    return 'just now';
  } else if (Math.abs(diffMinutes) < 60) {
    return diffMinutes > 0 ? `${diffMinutes}m ago` : `in ${Math.abs(diffMinutes)}m`;
  } else if (Math.abs(diffHours) < 24) {
    return diffHours > 0 ? `${diffHours}h ago` : `in ${Math.abs(diffHours)}h`;
  } else if (Math.abs(diffDays) < 7) {
    return diffDays > 0 ? `${diffDays}d ago` : `in ${Math.abs(diffDays)}d`;
  } else {
    return formatDate(date, 'short');
  }
};

/**
 * Format percentage
 * @param {number} value - Value to format as percentage
 * @param {number} decimals - Number of decimal places (default: 2)
 * @returns {string} Formatted percentage string
 */
export const formatPercentage = (value, decimals = 2) => {
  if (value === null || value === undefined || isNaN(value)) {
    return '0%';
  }

  return `${(value * 100).toFixed(decimals)}%`;
};

/**
 * Format file size
 * @param {number} bytes - Size in bytes
 * @param {number} decimals - Number of decimal places (default: 2)
 * @returns {string} Formatted file size string
 */
export const formatFileSize = (bytes, decimals = 2) => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
};

/**
 * Format phone number
 * @param {string} phoneNumber - Phone number to format
 * @param {string} format - Format type ('international', 'national', 'e164')
 * @returns {string} Formatted phone number
 */
export const formatPhoneNumber = (phoneNumber, format = 'international') => {
  if (!phoneNumber) return '';

  // Remove all non-digit characters
  const cleaned = phoneNumber.replace(/\D/g, '');

  // Basic formatting for common patterns
  if (format === 'international' && cleaned.length >= 10) {
    if (cleaned.startsWith('254')) {
      // Kenya format
      return `+${cleaned.slice(0, 3)} ${cleaned.slice(3, 6)} ${cleaned.slice(6)}`;
    } else if (cleaned.startsWith('1') && cleaned.length === 11) {
      // US format
      return `+${cleaned.slice(0, 1)} (${cleaned.slice(1, 4)}) ${cleaned.slice(4, 7)}-${cleaned.slice(7)}`;
    }
  }

  return phoneNumber;
};

/**
 * Format trade status for display
 * @param {string} status - Trade status
 * @returns {string} Formatted status string
 */
export const formatTradeStatus = (status) => {
  const statusMap = {
    'pending': 'Pending',
    'accepted': 'Accepted',
    'payment_sent': 'Payment Sent',
    'completed': 'Completed',
    'cancelled': 'Cancelled',
    'disputed': 'Disputed',
  };

  return statusMap[status] || status;
};

/**
 * Truncate text with ellipsis
 * @param {string} text - Text to truncate
 * @param {number} maxLength - Maximum length (default: 50)
 * @returns {string} Truncated text
 */
export const truncateText = (text, maxLength = 50) => {
  if (!text || text.length <= maxLength) return text;
  return text.substring(0, maxLength - 3) + '...';
};

/**
 * Format address for display (show first and last few characters)
 * @param {string} address - Crypto address
 * @param {number} startChars - Number of characters to show at start (default: 6)
 * @param {number} endChars - Number of characters to show at end (default: 4)
 * @returns {string} Formatted address
 */
export const formatAddress = (address, startChars = 6, endChars = 4) => {
  if (!address || address.length <= startChars + endChars) return address;
  return `${address.slice(0, startChars)}...${address.slice(-endChars)}`;
};

/**
 * Format number with thousand separators
 * @param {number} number - Number to format
 * @param {string} locale - Locale for formatting (default: 'en-US')
 * @returns {string} Formatted number string
 */
export const formatNumber = (number, locale = 'en-US') => {
  if (number === null || number === undefined || isNaN(number)) {
    return '0';
  }

  return new Intl.NumberFormat(locale).format(number);
};

// Default export for convenience
export default {
  formatCurrency,
  formatCrypto,
  formatDate,
  formatRelativeTime,
  formatPercentage,
  formatFileSize,
  formatPhoneNumber,
  formatTradeStatus,
  truncateText,
  formatAddress,
  formatNumber,
};
