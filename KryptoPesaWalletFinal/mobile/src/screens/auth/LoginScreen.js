import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView, KeyboardAvoidingView, Platform, Alert } from 'react-native';
import { Text, TextInput, Button, Card, IconButton, Divider } from 'react-native-paper';
import { useDispatch, useSelector } from 'react-redux';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { loginUser, selectAuthLoading } from '../../store/slices/authSlice';
import { selectApp } from '../../store/slices/appSlice';
import { biometricService } from '../../services/biometricService';
import { theme } from '../../utils/theme';

const LoginScreen = ({ navigation }) => {
  const dispatch = useDispatch();
  const isLoading = useSelector(selectAuthLoading);
  const app = useSelector(selectApp);

  const [identifier, setIdentifier] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [biometricAvailable, setBiometricAvailable] = useState(false);
  const [biometricEnabled, setBiometricEnabled] = useState(false);
  const [biometricType, setBiometricType] = useState(null);

  useEffect(() => {
    checkBiometricAvailability();
  }, []);

  const checkBiometricAvailability = async () => {
    try {
      const available = await biometricService.isBiometricAvailable();
      setBiometricAvailable(available);

      if (available) {
        const enabled = await biometricService.isBiometricEnabled();
        setBiometricEnabled(enabled);

        const type = await biometricService.getBiometricType();
        setBiometricType(type);
      }
    } catch (error) {
      console.error('Error checking biometric availability:', error);
    }
  };

  const handleLogin = () => {
    if (identifier.trim() && password.trim()) {
      dispatch(loginUser({ identifier: identifier.trim(), password }));
    }
  };

  const handleBiometricLogin = async () => {
    try {
      const result = await biometricService.authenticateForLogin();

      if (result.success) {
        // For demo purposes, we'll use stored credentials
        // In production, you'd retrieve the stored identifier
        const storedIdentifier = await biometricService.getStoredIdentifier();
        if (storedIdentifier) {
          dispatch(loginUser({ identifier: storedIdentifier, useBiometric: true }));
        } else {
          Alert.alert(
            'Setup Required',
            'Please sign in with your password first to enable biometric login.',
            [{ text: 'OK' }]
          );
        }
      } else {
        if (result.error && !result.error.includes('cancelled')) {
          Alert.alert('Authentication Failed', result.error);
        }
      }
    } catch (error) {
      console.error('Biometric login error:', error);
      Alert.alert('Error', 'Biometric authentication failed. Please try again.');
    }
  };

  const getBiometricIcon = () => {
    switch (biometricType) {
      case 'TouchID':
        return 'fingerprint';
      case 'FaceID':
        return 'face-recognition';
      case 'Biometrics':
        return 'fingerprint';
      default:
        return 'fingerprint';
    }
  };

  return (
    <KeyboardAvoidingView 
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <View style={styles.header}>
          <Text style={styles.title}>Welcome Back</Text>
          <Text style={styles.subtitle}>Sign in to your KryptoPesa account</Text>
        </View>

        <Card style={styles.formCard}>
          <Card.Content>
            <TextInput
              label="Email, Username, or Phone"
              value={identifier}
              onChangeText={setIdentifier}
              mode="outlined"
              style={styles.input}
              autoCapitalize="none"
              autoCorrect={false}
            />
            
            <TextInput
              label="Password"
              value={password}
              onChangeText={setPassword}
              mode="outlined"
              secureTextEntry={!showPassword}
              right={
                <TextInput.Icon 
                  icon={showPassword ? "eye-off" : "eye"} 
                  onPress={() => setShowPassword(!showPassword)}
                />
              }
              style={styles.input}
            />

            <Button
              mode="contained"
              onPress={handleLogin}
              loading={isLoading}
              disabled={isLoading || !identifier.trim() || !password.trim()}
              style={styles.loginButton}
            >
              Sign In
            </Button>

            {biometricAvailable && biometricEnabled && (
              <>
                <Divider style={styles.divider} />

                <View style={styles.biometricSection}>
                  <Text style={styles.biometricText}>
                    Or use {biometricService.getBiometricTypeLabel()}
                  </Text>

                  <IconButton
                    icon={getBiometricIcon()}
                    size={48}
                    mode="contained"
                    onPress={handleBiometricLogin}
                    disabled={isLoading}
                    style={styles.biometricButton}
                  />
                </View>

                <Divider style={styles.divider} />
              </>
            )}

            <Button
              mode="text"
              onPress={() => navigation.navigate('ForgotPassword')}
              style={styles.forgotButton}
            >
              Forgot Password?
            </Button>
          </Card.Content>
        </Card>

        <View style={styles.footer}>
          <Text style={styles.footerText}>Don't have an account?</Text>
          <Button
            mode="text"
            onPress={() => navigation.navigate('Register')}
          >
            Sign Up
          </Button>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  scrollContent: {
    flexGrow: 1,
    padding: theme.spacing.lg,
    justifyContent: 'center',
  },
  header: {
    alignItems: 'center',
    marginBottom: theme.spacing.xl,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: theme.colors.primary,
    marginBottom: theme.spacing.sm,
  },
  subtitle: {
    fontSize: 16,
    color: theme.colors.onSurfaceVariant,
    textAlign: 'center',
  },
  formCard: {
    marginBottom: theme.spacing.lg,
  },
  input: {
    marginBottom: theme.spacing.md,
  },
  loginButton: {
    marginTop: theme.spacing.md,
    marginBottom: theme.spacing.sm,
  },
  forgotButton: {
    alignSelf: 'center',
  },
  divider: {
    marginVertical: theme.spacing.md,
  },
  biometricSection: {
    alignItems: 'center',
    paddingVertical: theme.spacing.md,
  },
  biometricText: {
    fontSize: 14,
    color: theme.colors.onSurfaceVariant,
    marginBottom: theme.spacing.sm,
  },
  biometricButton: {
    backgroundColor: theme.colors.primaryContainer,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  footerText: {
    color: theme.colors.onSurfaceVariant,
  },
});

export default LoginScreen;
