import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Alert,
} from 'react-native';
import {
  Text,
  Card,
  Switch,
  Button,
  List,
  Divider,
  IconButton,
} from 'react-native-paper';
import { useSelector, useDispatch } from 'react-redux';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

import { theme } from '../utils/theme';
import { biometricService } from '../services/biometricService';
import { setBiometricEnabled } from '../store/slices/appSlice';
import { selectApp } from '../store/slices/appSlice';
import LoadingScreen from '../components/LoadingScreen';

const BiometricSettingsScreen = ({ navigation }) => {
  const dispatch = useDispatch();
  const app = useSelector(selectApp);

  const [isLoading, setIsLoading] = useState(true);
  const [biometricAvailable, setBiometricAvailable] = useState(false);
  const [biometricType, setBiometricType] = useState(null);
  const [biometricEnabled, setBiometricEnabledLocal] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);

  useEffect(() => {
    initializeBiometrics();
  }, []);

  const initializeBiometrics = async () => {
    try {
      setIsLoading(true);
      
      const available = await biometricService.isBiometricAvailable();
      setBiometricAvailable(available);
      
      if (available) {
        const type = await biometricService.getBiometricType();
        setBiometricType(type);
        
        const enabled = await biometricService.isBiometricEnabled();
        setBiometricEnabledLocal(enabled);
      }
    } catch (error) {
      console.error('Error initializing biometrics:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleToggleBiometric = async () => {
    try {
      setIsProcessing(true);

      if (biometricEnabled) {
        // Disable biometric authentication
        const success = await biometricService.disableBiometricAuth();
        if (success) {
          setBiometricEnabledLocal(false);
          dispatch(setBiometricEnabled(false));
          Alert.alert(
            'Biometric Authentication Disabled',
            'You will need to use your password to sign in.',
            [{ text: 'OK' }]
          );
        } else {
          Alert.alert('Error', 'Failed to disable biometric authentication.');
        }
      } else {
        // Enable biometric authentication
        const success = await biometricService.enableBiometricAuth();
        if (success) {
          setBiometricEnabledLocal(true);
          dispatch(setBiometricEnabled(true));
          Alert.alert(
            'Biometric Authentication Enabled',
            `You can now use ${biometricService.getBiometricTypeLabel()} to sign in quickly and securely.`,
            [{ text: 'OK' }]
          );
        }
        // If failed, the service will show appropriate error messages
      }
    } catch (error) {
      console.error('Error toggling biometric auth:', error);
      Alert.alert('Error', 'An unexpected error occurred. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleTestBiometric = async () => {
    try {
      setIsProcessing(true);
      
      const result = await biometricService.authenticateWithBiometrics(
        'Test biometric authentication'
      );
      
      if (result.success) {
        Alert.alert(
          'Success',
          'Biometric authentication test completed successfully!',
          [{ text: 'OK' }]
        );
      } else {
        Alert.alert(
          'Test Failed',
          result.error || 'Biometric authentication test failed.',
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.error('Error testing biometric auth:', error);
      Alert.alert('Error', 'Failed to test biometric authentication.');
    } finally {
      setIsProcessing(false);
    }
  };

  const getBiometricIcon = () => {
    switch (biometricType) {
      case 'TouchID':
        return 'fingerprint';
      case 'FaceID':
        return 'face-recognition';
      case 'Biometrics':
        return 'fingerprint';
      default:
        return 'security';
    }
  };

  if (isLoading) {
    return <LoadingScreen message="Checking biometric availability..." />;
  }

  if (!biometricAvailable) {
    return (
      <View style={styles.container}>
        <Card style={styles.card}>
          <Card.Content style={styles.unavailableContent}>
            <Icon name="security-off" size={64} color={theme.colors.onSurfaceVariant} />
            <Text style={styles.unavailableTitle}>Biometric Authentication Unavailable</Text>
            <Text style={styles.unavailableText}>
              Your device does not support biometric authentication or it is not set up.
              Please set up fingerprint or face recognition in your device settings.
            </Text>
            <Button
              mode="outlined"
              onPress={() => navigation.goBack()}
              style={styles.backButton}
            >
              Go Back
            </Button>
          </Card.Content>
        </Card>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <Card style={styles.card}>
        <Card.Content>
          <View style={styles.header}>
            <Icon name={getBiometricIcon()} size={48} color={theme.colors.primary} />
            <Text style={styles.title}>Biometric Authentication</Text>
            <Text style={styles.subtitle}>
              Use {biometricService.getBiometricTypeLabel()} for quick and secure access
            </Text>
          </View>

          <Divider style={styles.divider} />

          <List.Item
            title="Enable Biometric Authentication"
            description={`Use ${biometricService.getBiometricTypeLabel()} to sign in and authorize transactions`}
            left={(props) => <List.Icon {...props} icon={getBiometricIcon()} />}
            right={() => (
              <Switch
                value={biometricEnabled}
                onValueChange={handleToggleBiometric}
                disabled={isProcessing}
              />
            )}
          />

          {biometricEnabled && (
            <>
              <Divider style={styles.divider} />
              
              <List.Item
                title="Test Biometric Authentication"
                description="Verify that biometric authentication is working correctly"
                left={(props) => <List.Icon {...props} icon="test-tube" />}
                right={() => (
                  <IconButton
                    icon="play"
                    onPress={handleTestBiometric}
                    disabled={isProcessing}
                  />
                )}
                onPress={handleTestBiometric}
              />
            </>
          )}

          <Divider style={styles.divider} />

          <View style={styles.infoSection}>
            <Text style={styles.infoTitle}>Security Information</Text>
            <Text style={styles.infoText}>
              • Biometric data is stored securely on your device and never shared
            </Text>
            <Text style={styles.infoText}>
              • You can always use your password as an alternative
            </Text>
            <Text style={styles.infoText}>
              • Biometric authentication will be required for sensitive actions
            </Text>
            <Text style={styles.infoText}>
              • You can disable this feature at any time
            </Text>
          </View>
        </Card.Content>
      </Card>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
    padding: theme.spacing.md,
  },
  card: {
    marginBottom: theme.spacing.md,
  },
  header: {
    alignItems: 'center',
    paddingVertical: theme.spacing.lg,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginTop: theme.spacing.md,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: theme.colors.onSurfaceVariant,
    textAlign: 'center',
    marginTop: theme.spacing.sm,
  },
  divider: {
    marginVertical: theme.spacing.md,
  },
  infoSection: {
    marginTop: theme.spacing.md,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: theme.spacing.sm,
  },
  infoText: {
    fontSize: 14,
    color: theme.colors.onSurfaceVariant,
    marginBottom: theme.spacing.xs,
    lineHeight: 20,
  },
  unavailableContent: {
    alignItems: 'center',
    paddingVertical: theme.spacing.xl,
  },
  unavailableTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginTop: theme.spacing.md,
    textAlign: 'center',
  },
  unavailableText: {
    fontSize: 16,
    color: theme.colors.onSurfaceVariant,
    textAlign: 'center',
    marginTop: theme.spacing.md,
    lineHeight: 24,
  },
  backButton: {
    marginTop: theme.spacing.lg,
  },
});

export default BiometricSettingsScreen;
