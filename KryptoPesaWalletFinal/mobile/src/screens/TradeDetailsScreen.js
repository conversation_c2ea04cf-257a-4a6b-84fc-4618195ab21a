import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  RefreshControl,
  Alert,
} from 'react-native';
import {
  Text,
  Card,
  Button,
  Chip,
  Divider,
  IconButton,
  ProgressBar,
} from 'react-native-paper';
import { useSelector, useDispatch } from 'react-redux';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

import { theme } from '../utils/theme';
import { fetchTradeById, updateTradeStatus } from '../store/slices/tradeSlice';
import { selectUser } from '../store/slices/authSlice';
import LoadingScreen from '../components/LoadingScreen';
import { formatCurrency, formatDate } from '../utils/formatters';

const TradeDetailsScreen = ({ navigation, route }) => {
  const { tradeId } = route.params;
  const dispatch = useDispatch();
  const user = useSelector(selectUser);

  const [trade, setTrade] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);

  useEffect(() => {
    loadTradeDetails();
  }, [tradeId]);

  const loadTradeDetails = async () => {
    try {
      setIsLoading(true);
      const result = await dispatch(fetchTradeById(tradeId));
      if (result.payload) {
        setTrade(result.payload);
      }
    } catch (error) {
      console.error('Error loading trade details:', error);
      Alert.alert('Error', 'Failed to load trade details');
    } finally {
      setIsLoading(false);
    }
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    await loadTradeDetails();
    setIsRefreshing(false);
  };

  const handleStatusUpdate = async (newStatus) => {
    try {
      setIsUpdating(true);
      const result = await dispatch(updateTradeStatus({ tradeId, status: newStatus }));
      if (result.payload) {
        setTrade(result.payload);
        Alert.alert('Success', `Trade status updated to ${newStatus}`);
      }
    } catch (error) {
      console.error('Error updating trade status:', error);
      Alert.alert('Error', 'Failed to update trade status');
    } finally {
      setIsUpdating(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending': return theme.colors.warning;
      case 'funded': return theme.colors.info;
      case 'payment_sent': return theme.colors.primary;
      case 'payment_confirmed': return theme.colors.success;
      case 'completed': return theme.colors.success;
      case 'cancelled': return theme.colors.error;
      case 'disputed': return theme.colors.error;
      default: return theme.colors.onSurfaceVariant;
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'pending': return 'clock-outline';
      case 'funded': return 'lock';
      case 'payment_sent': return 'send';
      case 'payment_confirmed': return 'check-circle';
      case 'completed': return 'check-all';
      case 'cancelled': return 'close-circle';
      case 'disputed': return 'alert-circle';
      default: return 'help-circle';
    }
  };

  const getTradeProgress = (status) => {
    const statusOrder = ['pending', 'funded', 'payment_sent', 'payment_confirmed', 'completed'];
    const currentIndex = statusOrder.indexOf(status);
    return currentIndex >= 0 ? (currentIndex + 1) / statusOrder.length : 0;
  };

  const isUserSeller = () => {
    return trade && user && trade.seller._id === user._id;
  };

  const isUserBuyer = () => {
    return trade && user && trade.buyer._id === user._id;
  };

  const canUpdateStatus = (newStatus) => {
    if (!trade || isUpdating) return false;

    const currentStatus = trade.status;
    const userIsSeller = isUserSeller();
    const userIsBuyer = isUserBuyer();

    // Define allowed status transitions
    const allowedTransitions = {
      'pending': {
        'funded': userIsSeller,
        'cancelled': true,
      },
      'funded': {
        'payment_sent': userIsBuyer,
        'cancelled': true,
        'disputed': true,
      },
      'payment_sent': {
        'payment_confirmed': userIsSeller,
        'disputed': true,
      },
      'payment_confirmed': {
        'completed': true,
        'disputed': userIsBuyer,
      },
    };

    return allowedTransitions[currentStatus]?.[newStatus] || false;
  };

  if (isLoading) {
    return <LoadingScreen message="Loading trade details..." />;
  }

  if (!trade) {
    return (
      <View style={styles.errorContainer}>
        <Icon name="alert-circle" size={64} color={theme.colors.error} />
        <Text style={styles.errorTitle}>Trade Not Found</Text>
        <Text style={styles.errorText}>The requested trade could not be found.</Text>
        <Button mode="outlined" onPress={() => navigation.goBack()}>
          Go Back
        </Button>
      </View>
    );
  }

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={isRefreshing} onRefresh={handleRefresh} />
      }
    >
      {/* Trade Status Card */}
      <Card style={styles.card}>
        <Card.Content>
          <View style={styles.statusHeader}>
            <View style={styles.statusInfo}>
              <Text style={styles.tradeId}>Trade #{trade.tradeId}</Text>
              <Chip
                icon={getStatusIcon(trade.status)}
                style={[styles.statusChip, { backgroundColor: getStatusColor(trade.status) }]}
                textStyle={{ color: 'white' }}
              >
                {trade.status.replace('_', ' ').toUpperCase()}
              </Chip>
            </View>
            <IconButton
              icon="chat"
              onPress={() => navigation.navigate('Chat', { trade })}
            />
          </View>

          <ProgressBar
            progress={getTradeProgress(trade.status)}
            color={theme.colors.primary}
            style={styles.progressBar}
          />

          <Text style={styles.progressText}>
            Step {Math.floor(getTradeProgress(trade.status) * 5)} of 5
          </Text>
        </Card.Content>
      </Card>

      {/* Trade Details Card */}
      <Card style={styles.card}>
        <Card.Content>
          <Text style={styles.sectionTitle}>Trade Details</Text>

          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Amount:</Text>
            <Text style={styles.detailValue}>
              {trade.cryptocurrency.amount} {trade.cryptocurrency.symbol}
            </Text>
          </View>

          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Price:</Text>
            <Text style={styles.detailValue}>
              {formatCurrency(trade.fiat.amount, trade.fiat.currency)}
            </Text>
          </View>

          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Rate:</Text>
            <Text style={styles.detailValue}>
              {formatCurrency(trade.rate, trade.fiat.currency)} per {trade.cryptocurrency.symbol}
            </Text>
          </View>

          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Payment Method:</Text>
            <Text style={styles.detailValue}>{trade.paymentMethod}</Text>
          </View>

          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Created:</Text>
            <Text style={styles.detailValue}>{formatDate(trade.createdAt)}</Text>
          </View>
        </Card.Content>
      </Card>

      {/* Participants Card */}
      <Card style={styles.card}>
        <Card.Content>
          <Text style={styles.sectionTitle}>Participants</Text>

          <View style={styles.participantRow}>
            <View style={styles.participantInfo}>
              <Text style={styles.participantRole}>Seller</Text>
              <Text style={styles.participantName}>{trade.seller.username}</Text>
              <Text style={styles.participantRating}>
                ⭐ {trade.seller.reputation?.score || 'N/A'}
              </Text>
            </View>
            {isUserSeller() && (
              <Chip icon="account" style={styles.youChip}>You</Chip>
            )}
          </View>

          <Divider style={styles.divider} />

          <View style={styles.participantRow}>
            <View style={styles.participantInfo}>
              <Text style={styles.participantRole}>Buyer</Text>
              <Text style={styles.participantName}>{trade.buyer.username}</Text>
              <Text style={styles.participantRating}>
                ⭐ {trade.buyer.reputation?.score || 'N/A'}
              </Text>
            </View>
            {isUserBuyer() && (
              <Chip icon="account" style={styles.youChip}>You</Chip>
            )}
          </View>
        </Card.Content>
      </Card>

      {/* Action Buttons */}
      <Card style={styles.card}>
        <Card.Content>
          <Text style={styles.sectionTitle}>Actions</Text>

          <View style={styles.actionButtons}>
            {canUpdateStatus('funded') && (
              <Button
                mode="contained"
                onPress={() => handleStatusUpdate('funded')}
                loading={isUpdating}
                style={styles.actionButton}
              >
                Fund Escrow
              </Button>
            )}

            {canUpdateStatus('payment_sent') && (
              <Button
                mode="contained"
                onPress={() => navigation.navigate('PaymentProof', { trade })}
                style={styles.actionButton}
              >
                Mark Payment Sent
              </Button>
            )}

            {canUpdateStatus('payment_confirmed') && (
              <Button
                mode="contained"
                onPress={() => handleStatusUpdate('payment_confirmed')}
                loading={isUpdating}
                style={styles.actionButton}
              >
                Confirm Payment
              </Button>
            )}

            {canUpdateStatus('completed') && (
              <Button
                mode="contained"
                onPress={() => handleStatusUpdate('completed')}
                loading={isUpdating}
                style={styles.actionButton}
              >
                Complete Trade
              </Button>
            )}

            {canUpdateStatus('disputed') && (
              <Button
                mode="outlined"
                onPress={() => handleStatusUpdate('disputed')}
                loading={isUpdating}
                style={styles.actionButton}
                buttonColor={theme.colors.error}
              >
                Open Dispute
              </Button>
            )}

            {canUpdateStatus('cancelled') && (
              <Button
                mode="outlined"
                onPress={() => handleStatusUpdate('cancelled')}
                loading={isUpdating}
                style={styles.actionButton}
                buttonColor={theme.colors.error}
              >
                Cancel Trade
              </Button>
            )}
          </View>

          <Divider style={styles.divider} />

          <Button
            mode="outlined"
            onPress={() => navigation.navigate('Chat', { trade })}
            style={styles.chatButton}
            icon="chat"
          >
            Open Chat
          </Button>
        </Card.Content>
      </Card>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
    padding: theme.spacing.md,
  },
  card: {
    marginBottom: theme.spacing.md,
  },
  statusHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  statusInfo: {
    flex: 1,
  },
  tradeId: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: theme.spacing.sm,
  },
  statusChip: {
    alignSelf: 'flex-start',
  },
  progressBar: {
    height: 8,
    borderRadius: 4,
    marginBottom: theme.spacing.sm,
  },
  progressText: {
    fontSize: 12,
    color: theme.colors.onSurfaceVariant,
    textAlign: 'center',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: theme.spacing.md,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.sm,
  },
  detailLabel: {
    fontSize: 14,
    color: theme.colors.onSurfaceVariant,
  },
  detailValue: {
    fontSize: 14,
    fontWeight: '500',
  },
  participantRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: theme.spacing.sm,
  },
  participantInfo: {
    flex: 1,
  },
  participantRole: {
    fontSize: 12,
    color: theme.colors.onSurfaceVariant,
    textTransform: 'uppercase',
  },
  participantName: {
    fontSize: 16,
    fontWeight: 'bold',
    marginTop: theme.spacing.xs,
  },
  participantRating: {
    fontSize: 14,
    color: theme.colors.onSurfaceVariant,
    marginTop: theme.spacing.xs,
  },
  youChip: {
    backgroundColor: theme.colors.primaryContainer,
  },
  divider: {
    marginVertical: theme.spacing.sm,
  },
  actionButtons: {
    marginBottom: theme.spacing.md,
  },
  actionButton: {
    marginBottom: theme.spacing.sm,
  },
  chatButton: {
    marginTop: theme.spacing.sm,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.background,
    padding: theme.spacing.md,
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginTop: theme.spacing.md,
    marginBottom: theme.spacing.sm,
  },
  errorText: {
    fontSize: 16,
    color: theme.colors.onSurfaceVariant,
    textAlign: 'center',
    marginBottom: theme.spacing.lg,
  },
});

export default TradeDetailsScreen;
