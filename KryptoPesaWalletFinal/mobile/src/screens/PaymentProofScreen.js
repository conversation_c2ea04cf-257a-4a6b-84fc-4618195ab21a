import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Image,
  Alert,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import {
  Text,
  Card,
  Button,
  FAB,
  IconButton,
  Chip,
  Portal,
  Modal,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

import { theme } from '../utils/theme';
import { cameraService } from '../services/cameraService';
import LoadingScreen from '../components/LoadingScreen';

const { width } = Dimensions.get('window');
const imageSize = (width - theme.spacing.md * 4) / 3;

const PaymentProofScreen = ({ navigation, route }) => {
  const { trade } = route.params;
  
  const [isLoading, setIsLoading] = useState(true);
  const [paymentProofs, setPaymentProofs] = useState([]);
  const [isCapturing, setIsCapturing] = useState(false);
  const [selectedImage, setSelectedImage] = useState(null);
  const [modalVisible, setModalVisible] = useState(false);

  useEffect(() => {
    loadPaymentProofs();
    initializeCameraService();
  }, []);

  const initializeCameraService = async () => {
    try {
      await cameraService.initialize();
    } catch (error) {
      console.error('Error initializing camera service:', error);
    }
  };

  const loadPaymentProofs = async () => {
    try {
      setIsLoading(true);
      const proofs = await cameraService.getPaymentProofs(trade.tradeId);
      setPaymentProofs(proofs);
    } catch (error) {
      console.error('Error loading payment proofs:', error);
      Alert.alert('Error', 'Failed to load payment proofs.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCaptureProof = async () => {
    try {
      setIsCapturing(true);
      
      const proof = await cameraService.capturePaymentProof(trade.tradeId);
      
      if (proof) {
        setPaymentProofs(prev => [...prev, proof]);
        Alert.alert(
          'Success',
          'Payment proof captured successfully!',
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.error('Error capturing payment proof:', error);
      Alert.alert('Error', 'Failed to capture payment proof. Please try again.');
    } finally {
      setIsCapturing(false);
    }
  };

  const handleDeleteProof = async (proof) => {
    Alert.alert(
      'Delete Payment Proof',
      'Are you sure you want to delete this payment proof?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              const success = await cameraService.deletePaymentProof(proof);
              if (success) {
                setPaymentProofs(prev => prev.filter(p => p.id !== proof.id));
              } else {
                Alert.alert('Error', 'Failed to delete payment proof.');
              }
            } catch (error) {
              console.error('Error deleting payment proof:', error);
              Alert.alert('Error', 'Failed to delete payment proof.');
            }
          },
        },
      ]
    );
  };

  const handleImagePress = (proof) => {
    setSelectedImage(proof);
    setModalVisible(true);
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatTimestamp = (timestamp) => {
    return new Date(timestamp).toLocaleString();
  };

  if (isLoading) {
    return <LoadingScreen message="Loading payment proofs..." />;
  }

  return (
    <View style={styles.container}>
      <ScrollView style={styles.scrollView}>
        <Card style={styles.infoCard}>
          <Card.Content>
            <View style={styles.tradeInfo}>
              <Text style={styles.tradeTitle}>Trade #{trade.tradeId}</Text>
              <Text style={styles.tradeAmount}>
                {trade.cryptocurrency.amount} {trade.cryptocurrency.symbol} for{' '}
                {trade.fiat.amount} {trade.fiat.currency}
              </Text>
            </View>
            
            <Text style={styles.instructionText}>
              Upload proof of payment to confirm that you have sent the payment to the seller.
              This helps build trust and resolve any disputes.
            </Text>
          </Card.Content>
        </Card>

        {paymentProofs.length > 0 && (
          <Card style={styles.proofsCard}>
            <Card.Content>
              <Text style={styles.sectionTitle}>Payment Proofs ({paymentProofs.length})</Text>
              
              <View style={styles.imageGrid}>
                {paymentProofs.map((proof, index) => (
                  <TouchableOpacity
                    key={proof.id}
                    style={styles.imageContainer}
                    onPress={() => handleImagePress(proof)}
                  >
                    <Image
                      source={{ uri: `file://${proof.localPath}` }}
                      style={styles.proofImage}
                      resizeMode="cover"
                    />
                    <View style={styles.imageOverlay}>
                      <IconButton
                        icon="delete"
                        size={20}
                        iconColor="white"
                        style={styles.deleteButton}
                        onPress={() => handleDeleteProof(proof)}
                      />
                    </View>
                    <View style={styles.imageInfo}>
                      <Text style={styles.imageIndex}>{index + 1}</Text>
                    </View>
                  </TouchableOpacity>
                ))}
              </View>
            </Card.Content>
          </Card>
        )}

        {paymentProofs.length === 0 && (
          <Card style={styles.emptyCard}>
            <Card.Content style={styles.emptyContent}>
              <Icon name="camera-plus" size={64} color={theme.colors.onSurfaceVariant} />
              <Text style={styles.emptyTitle}>No Payment Proofs</Text>
              <Text style={styles.emptyText}>
                Add photos of your payment receipts, bank transfer confirmations, or mobile money transactions.
              </Text>
            </Card.Content>
          </Card>
        )}

        <Card style={styles.tipsCard}>
          <Card.Content>
            <Text style={styles.tipsTitle}>Tips for Good Payment Proofs</Text>
            <View style={styles.tipsList}>
              <Text style={styles.tipItem}>• Include transaction reference numbers</Text>
              <Text style={styles.tipItem}>• Show recipient details clearly</Text>
              <Text style={styles.tipItem}>• Capture the full screen/receipt</Text>
              <Text style={styles.tipItem}>• Ensure text is readable</Text>
              <Text style={styles.tipItem}>• Include timestamp if visible</Text>
            </View>
          </Card.Content>
        </Card>
      </ScrollView>

      {/* Add Payment Proof FAB */}
      <FAB
        style={styles.fab}
        icon="camera-plus"
        label="Add Proof"
        onPress={handleCaptureProof}
        loading={isCapturing}
        disabled={isCapturing}
      />

      {/* Image Preview Modal */}
      <Portal>
        <Modal
          visible={modalVisible}
          onDismiss={() => setModalVisible(false)}
          contentContainerStyle={styles.modalContainer}
        >
          {selectedImage && (
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>Payment Proof</Text>
                <IconButton
                  icon="close"
                  onPress={() => setModalVisible(false)}
                />
              </View>
              
              <Image
                source={{ uri: `file://${selectedImage.localPath}` }}
                style={styles.fullImage}
                resizeMode="contain"
              />
              
              <View style={styles.modalInfo}>
                <Chip icon="clock" style={styles.infoChip}>
                  {formatTimestamp(selectedImage.timestamp)}
                </Chip>
                <Chip icon="file" style={styles.infoChip}>
                  {formatFileSize(selectedImage.size)}
                </Chip>
              </View>
              
              <Button
                mode="outlined"
                onPress={() => handleDeleteProof(selectedImage)}
                style={styles.deleteModalButton}
                textColor={theme.colors.error}
              >
                Delete This Proof
              </Button>
            </View>
          )}
        </Modal>
      </Portal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  scrollView: {
    flex: 1,
    padding: theme.spacing.md,
  },
  infoCard: {
    marginBottom: theme.spacing.md,
  },
  tradeInfo: {
    marginBottom: theme.spacing.md,
  },
  tradeTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  tradeAmount: {
    fontSize: 16,
    color: theme.colors.onSurfaceVariant,
    marginTop: theme.spacing.xs,
  },
  instructionText: {
    fontSize: 14,
    lineHeight: 20,
    color: theme.colors.onSurfaceVariant,
  },
  proofsCard: {
    marginBottom: theme.spacing.md,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: theme.spacing.md,
  },
  imageGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  imageContainer: {
    width: imageSize,
    height: imageSize,
    marginBottom: theme.spacing.sm,
    borderRadius: theme.roundness,
    overflow: 'hidden',
    position: 'relative',
  },
  proofImage: {
    width: '100%',
    height: '100%',
  },
  imageOverlay: {
    position: 'absolute',
    top: 0,
    right: 0,
  },
  deleteButton: {
    backgroundColor: 'rgba(0,0,0,0.5)',
    margin: 4,
  },
  imageInfo: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    backgroundColor: 'rgba(0,0,0,0.7)',
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xs,
  },
  imageIndex: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  emptyCard: {
    marginBottom: theme.spacing.md,
  },
  emptyContent: {
    alignItems: 'center',
    paddingVertical: theme.spacing.xl,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: theme.spacing.md,
  },
  emptyText: {
    fontSize: 14,
    color: theme.colors.onSurfaceVariant,
    textAlign: 'center',
    marginTop: theme.spacing.sm,
    lineHeight: 20,
  },
  tipsCard: {
    marginBottom: theme.spacing.xl,
  },
  tipsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: theme.spacing.sm,
  },
  tipsList: {
    marginLeft: theme.spacing.sm,
  },
  tipItem: {
    fontSize: 14,
    color: theme.colors.onSurfaceVariant,
    marginBottom: theme.spacing.xs,
    lineHeight: 18,
  },
  fab: {
    position: 'absolute',
    margin: theme.spacing.md,
    right: 0,
    bottom: 0,
  },
  modalContainer: {
    backgroundColor: theme.colors.surface,
    margin: theme.spacing.md,
    borderRadius: theme.roundness,
    maxHeight: '90%',
  },
  modalContent: {
    padding: theme.spacing.md,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  fullImage: {
    width: '100%',
    height: 300,
    marginBottom: theme.spacing.md,
  },
  modalInfo: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: theme.spacing.md,
  },
  infoChip: {
    marginHorizontal: theme.spacing.xs,
  },
  deleteModalButton: {
    borderColor: theme.colors.error,
  },
});

export default PaymentProofScreen;
