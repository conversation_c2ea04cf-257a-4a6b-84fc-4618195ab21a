import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  Alert,
  Linking,
  Platform,
  StatusBar,
} from 'react-native';
import {
  Text,
  Button,
  IconButton,
  Surface,
  TextInput,
  Portal,
  Modal,
} from 'react-native-paper';
import { Camera, useCameraDevices } from 'react-native-vision-camera';
import { useScanBarcodes, BarcodeFormat } from 'vision-camera-code-scanner';
import { check, request, PERMISSIONS, RESULTS } from 'react-native-permissions';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

import { theme } from '../utils/theme';

const QRScannerScreen = ({ navigation, route }) => {
  const [hasPermission, setHasPermission] = useState(false);
  const [isActive, setIsActive] = useState(true);
  const [scannedData, setScannedData] = useState(null);
  const [manualInputVisible, setManualInputVisible] = useState(false);
  const [manualAddress, setManualAddress] = useState('');
  const [flashEnabled, setFlashEnabled] = useState(false);

  const devices = useCameraDevices();
  const device = devices.back;

  const { onScan } = route.params || {};

  // Configure barcode scanner
  const [frameProcessor, barcodes] = useScanBarcodes([
    BarcodeFormat.QR_CODE,
  ], {
    checkInverted: true,
  });

  useEffect(() => {
    checkCameraPermission();
  }, []);

  useEffect(() => {
    if (barcodes && barcodes.length > 0 && isActive) {
      const qrCode = barcodes[0];
      handleQRCodeScanned(qrCode.displayValue);
    }
  }, [barcodes, isActive]);

  // Cleanup camera on unmount to prevent memory leaks
  useEffect(() => {
    return () => {
      setIsActive(false);
      setScannedData(null);
      setFlashEnabled(false);
    };
  }, []);

  const checkCameraPermission = async () => {
    try {
      const permission = Platform.OS === 'ios' 
        ? PERMISSIONS.IOS.CAMERA 
        : PERMISSIONS.ANDROID.CAMERA;

      const result = await check(permission);
      
      if (result === RESULTS.GRANTED) {
        setHasPermission(true);
      } else if (result === RESULTS.DENIED) {
        requestCameraPermission();
      } else {
        setHasPermission(false);
        Alert.alert(
          'Camera Permission Required',
          'Please enable camera permission in settings to scan QR codes.',
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Open Settings', onPress: () => Linking.openSettings() },
          ]
        );
      }
    } catch (error) {
      console.log('Permission check error:', error);
      setHasPermission(false);
    }
  };

  const requestCameraPermission = async () => {
    try {
      const permission = Platform.OS === 'ios' 
        ? PERMISSIONS.IOS.CAMERA 
        : PERMISSIONS.ANDROID.CAMERA;

      const result = await request(permission);
      setHasPermission(result === RESULTS.GRANTED);
      
      if (result !== RESULTS.GRANTED) {
        Alert.alert(
          'Camera Permission Denied',
          'Camera access is required to scan QR codes. You can enter the address manually instead.',
          [
            { text: 'Manual Input', onPress: () => setManualInputVisible(true) },
            { text: 'Try Again', onPress: checkCameraPermission },
          ]
        );
      }
    } catch (error) {
      console.log('Permission request error:', error);
      setHasPermission(false);
    }
  };

  const handleQRCodeScanned = (data) => {
    if (!data || scannedData === data) return;
    
    setScannedData(data);
    setIsActive(false);

    // Parse QR code data
    const parsedData = parseQRCode(data);
    
    if (parsedData.isValid) {
      Alert.alert(
        'QR Code Scanned',
        `Address: ${parsedData.address}\n${parsedData.amount ? `Amount: ${parsedData.amount}` : ''}`,
        [
          { text: 'Cancel', onPress: resetScanner },
          { 
            text: 'Use Address', 
            onPress: () => {
              if (onScan) {
                onScan(parsedData.address, parsedData.amount);
              }
              navigation.goBack();
            }
          },
        ]
      );
    } else {
      Alert.alert(
        'Invalid QR Code',
        'This QR code does not contain a valid cryptocurrency address.',
        [
          { text: 'Try Again', onPress: resetScanner },
          { text: 'Manual Input', onPress: () => setManualInputVisible(true) },
        ]
      );
    }
  };

  const parseQRCode = (data) => {
    try {
      // Handle different QR code formats
      let address = data;
      let amount = null;
      let isValid = false;

      // Bitcoin URI format: bitcoin:address?amount=0.1
      if (data.startsWith('bitcoin:')) {
        const url = new URL(data);
        address = url.pathname;
        amount = url.searchParams.get('amount');
        isValid = isValidBitcoinAddress(address);
      }
      // Ethereum URI format: ethereum:address?value=1000000000000000000
      else if (data.startsWith('ethereum:')) {
        const url = new URL(data);
        address = url.pathname;
        const value = url.searchParams.get('value');
        if (value) {
          amount = (parseInt(value) / Math.pow(10, 18)).toString(); // Convert wei to ETH
        }
        isValid = isValidEthereumAddress(address);
      }
      // Plain address
      else {
        address = data.trim();
        isValid = isValidEthereumAddress(address) || isValidBitcoinAddress(address);
      }

      return { address, amount, isValid };
    } catch (error) {
      console.log('QR parse error:', error);
      return { address: data, amount: null, isValid: false };
    }
  };

  const isValidEthereumAddress = (address) => {
    return /^0x[a-fA-F0-9]{40}$/.test(address);
  };

  const isValidBitcoinAddress = (address) => {
    // Simplified Bitcoin address validation
    return /^[13][a-km-zA-HJ-NP-Z1-9]{25,34}$/.test(address) || // Legacy
           /^bc1[a-z0-9]{39,59}$/.test(address); // Bech32
  };

  const resetScanner = () => {
    setScannedData(null);
    setIsActive(true);
  };

  const handleManualSubmit = () => {
    if (!manualAddress.trim()) {
      Alert.alert('Error', 'Please enter a valid address');
      return;
    }

    const parsedData = parseQRCode(manualAddress.trim());
    
    if (parsedData.isValid) {
      if (onScan) {
        onScan(parsedData.address, parsedData.amount);
      }
      navigation.goBack();
    } else {
      Alert.alert('Invalid Address', 'Please enter a valid cryptocurrency address');
    }
  };

  const toggleFlash = () => {
    setFlashEnabled(!flashEnabled);
  };

  if (!hasPermission) {
    return (
      <View style={styles.permissionContainer}>
        <Icon name="camera-off" size={64} color={theme.colors.onSurfaceVariant} />
        <Text style={styles.permissionTitle}>Camera Permission Required</Text>
        <Text style={styles.permissionText}>
          To scan QR codes, please allow camera access in your device settings.
        </Text>
        <Button mode="contained" onPress={checkCameraPermission} style={styles.permissionButton}>
          Grant Permission
        </Button>
        <Button mode="outlined" onPress={() => setManualInputVisible(true)} style={styles.manualButton}>
          Enter Address Manually
        </Button>
      </View>
    );
  }

  if (!device) {
    return (
      <View style={styles.permissionContainer}>
        <Icon name="camera-off" size={64} color={theme.colors.onSurfaceVariant} />
        <Text style={styles.permissionTitle}>Camera Not Available</Text>
        <Text style={styles.permissionText}>
          No camera device found on this device.
        </Text>
        <Button mode="outlined" onPress={() => setManualInputVisible(true)} style={styles.manualButton}>
          Enter Address Manually
        </Button>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="black" />
      
      {/* Camera View */}
      <Camera
        style={styles.camera}
        device={device}
        isActive={isActive && hasPermission}
        frameProcessor={frameProcessor}
        frameProcessorFps={5}
        torch={flashEnabled ? 'on' : 'off'}
      />

      {/* Overlay */}
      <View style={styles.overlay}>
        {/* Header */}
        <View style={styles.header}>
          <IconButton
            icon="close"
            iconColor="white"
            size={24}
            onPress={() => navigation.goBack()}
          />
          <Text style={styles.headerTitle}>Scan QR Code</Text>
          <IconButton
            icon={flashEnabled ? "flash" : "flash-off"}
            iconColor="white"
            size={24}
            onPress={toggleFlash}
          />
        </View>

        {/* Scanning Area */}
        <View style={styles.scanningArea}>
          <View style={styles.scanFrame} />
          <Text style={styles.instructionText}>
            Position the QR code within the frame
          </Text>
        </View>

        {/* Bottom Actions */}
        <View style={styles.bottomActions}>
          <Button
            mode="contained"
            onPress={() => setManualInputVisible(true)}
            style={styles.manualInputButton}
            icon="keyboard"
          >
            Enter Manually
          </Button>
        </View>
      </View>

      {/* Manual Input Modal */}
      <Portal>
        <Modal
          visible={manualInputVisible}
          onDismiss={() => setManualInputVisible(false)}
          contentContainerStyle={styles.modalContainer}
        >
          <Text style={styles.modalTitle}>Enter Address Manually</Text>
          <TextInput
            label="Wallet Address"
            value={manualAddress}
            onChangeText={setManualAddress}
            mode="outlined"
            style={styles.manualInput}
            placeholder="0x... or bc1..."
            multiline
          />
          <View style={styles.modalActions}>
            <Button
              mode="outlined"
              onPress={() => setManualInputVisible(false)}
              style={styles.modalButton}
            >
              Cancel
            </Button>
            <Button
              mode="contained"
              onPress={handleManualSubmit}
              style={styles.modalButton}
            >
              Use Address
            </Button>
          </View>
        </Modal>
      </Portal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'black',
  },
  camera: {
    flex: 1,
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'transparent',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: Platform.OS === 'ios' ? 50 : 20,
    paddingHorizontal: theme.spacing.md,
    paddingBottom: theme.spacing.md,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: 'white',
  },
  scanningArea: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.xl,
  },
  scanFrame: {
    width: 250,
    height: 250,
    borderWidth: 2,
    borderColor: 'white',
    borderRadius: theme.roundness,
    backgroundColor: 'transparent',
  },
  instructionText: {
    color: 'white',
    fontSize: 16,
    textAlign: 'center',
    marginTop: theme.spacing.lg,
    backgroundColor: 'rgba(0,0,0,0.5)',
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    borderRadius: theme.roundness,
  },
  bottomActions: {
    paddingHorizontal: theme.spacing.lg,
    paddingBottom: Platform.OS === 'ios' ? 40 : theme.spacing.lg,
  },
  manualInputButton: {
    backgroundColor: theme.colors.primary,
  },
  permissionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: theme.spacing.xl,
    backgroundColor: theme.colors.background,
  },
  permissionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.onSurface,
    marginTop: theme.spacing.md,
    marginBottom: theme.spacing.sm,
  },
  permissionText: {
    fontSize: 14,
    color: theme.colors.onSurfaceVariant,
    textAlign: 'center',
    marginBottom: theme.spacing.lg,
    lineHeight: 20,
  },
  permissionButton: {
    marginBottom: theme.spacing.md,
    paddingHorizontal: theme.spacing.lg,
  },
  manualButton: {
    paddingHorizontal: theme.spacing.lg,
  },
  modalContainer: {
    backgroundColor: theme.colors.surface,
    margin: theme.spacing.lg,
    padding: theme.spacing.lg,
    borderRadius: theme.roundness,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.onSurface,
    marginBottom: theme.spacing.lg,
    textAlign: 'center',
  },
  manualInput: {
    marginBottom: theme.spacing.lg,
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: theme.spacing.md,
  },
  modalButton: {
    flex: 1,
  },
});

export default QRScannerScreen;
