import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Alert,
  RefreshControl,
} from 'react-native';
import {
  Text,
  Card,
  Button,
  Avatar,
  List,
  Divider,
  Switch,
  IconButton,
  Chip,
} from 'react-native-paper';
import { useSelector, useDispatch } from 'react-redux';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

import { theme } from '../utils/theme';
import { selectUser, logoutUser, updateUserProfile } from '../store/slices/authSlice';
import { selectApp, setBiometricEnabled, setNotificationsEnabled } from '../store/slices/appSlice';
import { biometricService } from '../services/biometricService';
import { notificationService } from '../services/notificationService';
import LoadingScreen from '../components/LoadingScreen';

const ProfileScreen = ({ navigation }) => {
  const dispatch = useDispatch();
  const user = useSelector(selectUser);
  const app = useSelector(selectApp);

  const [isLoading, setIsLoading] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [biometricAvailable, setBiometricAvailable] = useState(false);
  const [notificationsAvailable, setNotificationsAvailable] = useState(false);

  useEffect(() => {
    checkFeatureAvailability();
  }, []);

  const checkFeatureAvailability = async () => {
    try {
      const biometricSupported = await biometricService.isBiometricAvailable();
      setBiometricAvailable(biometricSupported);

      const notificationsSupported = await notificationService.areNotificationsEnabled();
      setNotificationsAvailable(notificationsSupported);
    } catch (error) {
      console.error('Error checking feature availability:', error);
    }
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    await checkFeatureAvailability();
    setIsRefreshing(false);
  };

  const handleBiometricToggle = async (enabled) => {
    try {
      if (enabled) {
        const success = await biometricService.enableBiometricAuth(user.email);
        if (success) {
          dispatch(setBiometricEnabled(true));
        }
      } else {
        const success = await biometricService.disableBiometricAuth();
        if (success) {
          dispatch(setBiometricEnabled(false));
        }
      }
    } catch (error) {
      console.error('Error toggling biometric auth:', error);
      Alert.alert('Error', 'Failed to update biometric authentication setting');
    }
  };

  const handleNotificationsToggle = async (enabled) => {
    try {
      if (enabled) {
        const hasPermission = await notificationService.requestPermissions();
        if (hasPermission) {
          dispatch(setNotificationsEnabled(true));
        } else {
          Alert.alert(
            'Permission Required',
            'Please enable notifications in your device settings',
            [
              { text: 'Cancel', style: 'cancel' },
              { text: 'Open Settings', onPress: () => notificationService.openNotificationSettings() },
            ]
          );
        }
      } else {
        dispatch(setNotificationsEnabled(false));
      }
    } catch (error) {
      console.error('Error toggling notifications:', error);
      Alert.alert('Error', 'Failed to update notification setting');
    }
  };

  const handleLogout = () => {
    Alert.alert(
      'Sign Out',
      'Are you sure you want to sign out?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Sign Out',
          style: 'destructive',
          onPress: () => {
            dispatch(logoutUser());
          },
        },
      ]
    );
  };

  const getReputationColor = (score) => {
    if (score >= 4.5) return theme.colors.success;
    if (score >= 3.5) return theme.colors.warning;
    return theme.colors.error;
  };

  const getVerificationStatus = () => {
    if (user.verification?.isVerified) {
      return { icon: 'check-circle', color: theme.colors.success, text: 'Verified' };
    }
    return { icon: 'clock-outline', color: theme.colors.warning, text: 'Pending' };
  };

  if (!user) {
    return <LoadingScreen message="Loading profile..." />;
  }

  const verification = getVerificationStatus();

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={isRefreshing} onRefresh={handleRefresh} />
      }
    >
      {/* Profile Header */}
      <Card style={styles.card}>
        <Card.Content style={styles.profileHeader}>
          <Avatar.Text
            size={80}
            label={user.username.substring(0, 2).toUpperCase()}
            style={styles.avatar}
          />

          <View style={styles.profileInfo}>
            <Text style={styles.username}>{user.username}</Text>
            <Text style={styles.email}>{user.email}</Text>

            <View style={styles.verificationRow}>
              <Icon name={verification.icon} size={16} color={verification.color} />
              <Text style={[styles.verificationText, { color: verification.color }]}>
                {verification.text}
              </Text>
            </View>

            {user.reputation && (
              <View style={styles.reputationRow}>
                <Icon name="star" size={16} color={getReputationColor(user.reputation.score)} />
                <Text style={styles.reputationText}>
                  {user.reputation.score.toFixed(1)} ({user.reputation.totalTrades} trades)
                </Text>
              </View>
            )}
          </View>

          <IconButton
            icon="pencil"
            onPress={() => navigation.navigate('EditProfile')}
          />
        </Card.Content>
      </Card>

      {/* Account Stats */}
      <Card style={styles.card}>
        <Card.Content>
          <Text style={styles.sectionTitle}>Account Statistics</Text>

          <View style={styles.statsRow}>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{user.stats?.totalTrades || 0}</Text>
              <Text style={styles.statLabel}>Total Trades</Text>
            </View>

            <View style={styles.statItem}>
              <Text style={styles.statValue}>{user.stats?.completedTrades || 0}</Text>
              <Text style={styles.statLabel}>Completed</Text>
            </View>

            <View style={styles.statItem}>
              <Text style={styles.statValue}>
                {user.stats?.totalTrades ?
                  Math.round((user.stats.completedTrades / user.stats.totalTrades) * 100) : 0}%
              </Text>
              <Text style={styles.statLabel}>Success Rate</Text>
            </View>
          </View>
        </Card.Content>
      </Card>

      {/* Settings */}
      <Card style={styles.card}>
        <Card.Content>
          <Text style={styles.sectionTitle}>Settings</Text>

          {biometricAvailable && (
            <List.Item
              title="Biometric Authentication"
              description="Use fingerprint or face recognition to sign in"
              left={(props) => <List.Icon {...props} icon="fingerprint" />}
              right={() => (
                <Switch
                  value={app.biometricEnabled}
                  onValueChange={handleBiometricToggle}
                />
              )}
            />
          )}

          <List.Item
            title="Push Notifications"
            description="Receive notifications for trades and messages"
            left={(props) => <List.Icon {...props} icon="bell" />}
            right={() => (
              <Switch
                value={app.notificationsEnabled}
                onValueChange={handleNotificationsToggle}
              />
            )}
          />

          <Divider style={styles.divider} />

          <List.Item
            title="Security Settings"
            description="Manage your account security"
            left={(props) => <List.Icon {...props} icon="shield-account" />}
            right={(props) => <List.Icon {...props} icon="chevron-right" />}
            onPress={() => navigation.navigate('BiometricSettings')}
          />

          <List.Item
            title="Help Center"
            description="Get help and find answers"
            left={(props) => <List.Icon {...props} icon="help-circle" />}
            right={(props) => <List.Icon {...props} icon="chevron-right" />}
            onPress={() => navigation.navigate('HelpCenter')}
          />
        </Card.Content>
      </Card>

      {/* Sign Out */}
      <Card style={styles.card}>
        <Card.Content>
          <Button
            mode="outlined"
            onPress={handleLogout}
            icon="logout"
            style={styles.logoutButton}
            buttonColor={theme.colors.error}
            textColor={theme.colors.error}
          >
            Sign Out
          </Button>
        </Card.Content>
      </Card>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
    padding: theme.spacing.md,
  },
  card: {
    marginBottom: theme.spacing.md,
  },
  profileHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    backgroundColor: theme.colors.primary,
  },
  profileInfo: {
    flex: 1,
    marginLeft: theme.spacing.md,
  },
  username: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  email: {
    fontSize: 14,
    color: theme.colors.onSurfaceVariant,
    marginTop: theme.spacing.xs,
  },
  verificationRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: theme.spacing.sm,
  },
  verificationText: {
    fontSize: 12,
    marginLeft: theme.spacing.xs,
    fontWeight: '500',
  },
  reputationRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: theme.spacing.xs,
  },
  reputationText: {
    fontSize: 12,
    color: theme.colors.onSurfaceVariant,
    marginLeft: theme.spacing.xs,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: theme.spacing.md,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.primary,
  },
  statLabel: {
    fontSize: 12,
    color: theme.colors.onSurfaceVariant,
    marginTop: theme.spacing.xs,
  },
  divider: {
    marginVertical: theme.spacing.sm,
  },
  logoutButton: {
    borderColor: theme.colors.error,
  },
});

export default ProfileScreen;
