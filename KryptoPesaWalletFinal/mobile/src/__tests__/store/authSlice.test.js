import { configureStore } from '@reduxjs/toolkit';
import authReducer, {
  loginUser,
  registerUser,
  loginWithBiometrics,
  enableBiometricAuth,
  disableBiometricAuth,
  clearError,
  updateUser,
  setBiometricEnabled,
  selectAuth,
  selectUser,
  selectIsAuthenticated,
} from '../../store/slices/authSlice';

// Mock API service
jest.mock('../../services/authService', () => ({
  authService: {
    login: jest.fn(),
    register: jest.fn(),
    loginWithBiometrics: jest.fn(),
    getCurrentUser: jest.fn(),
    logout: jest.fn(),
  },
}));

describe('authSlice', () => {
  let store;

  beforeEach(() => {
    store = configureStore({
      reducer: {
        auth: authReducer,
      },
    });
  });

  describe('initial state', () => {
    it('should have correct initial state', () => {
      const state = store.getState().auth;
      
      expect(state.user).toBeNull();
      expect(state.token).toBeNull();
      expect(state.isAuthenticated).toBe(false);
      expect(state.isLoading).toBe(false);
      expect(state.error).toBeNull();
      expect(state.loginAttempts).toBe(0);
      expect(state.lastLoginAttempt).toBeNull();
      expect(state.biometricEnabled).toBe(false);
      expect(state.biometricAvailable).toBe(false);
    });
  });

  describe('reducers', () => {
    it('should clear error', () => {
      // Set an error first
      store.dispatch({ type: 'auth/loginUser/rejected', payload: 'Test error' });
      expect(store.getState().auth.error).toBe('Test error');

      // Clear the error
      store.dispatch(clearError());
      expect(store.getState().auth.error).toBeNull();
    });

    it('should update user', () => {
      const userUpdate = {
        profile: {
          firstName: 'Updated',
          lastName: 'Name',
        },
      };

      store.dispatch(updateUser(userUpdate));
      expect(store.getState().auth.user).toEqual(userUpdate);
    });

    it('should set biometric enabled', () => {
      store.dispatch(setBiometricEnabled(true));
      expect(store.getState().auth.biometricEnabled).toBe(true);

      store.dispatch(setBiometricEnabled(false));
      expect(store.getState().auth.biometricEnabled).toBe(false);
    });
  });

  describe('async thunks', () => {
    describe('loginUser', () => {
      it('should handle successful login', async () => {
        const mockResponse = {
          data: {
            success: true,
            data: {
              user: testUser,
              token: 'mock-token',
            },
          },
        };

        const { authService } = require('../../services/authService');
        authService.login.mockResolvedValue(mockResponse);

        const action = await store.dispatch(
          loginUser({ identifier: 'testuser', password: 'password123' })
        );

        expect(action.type).toBe('auth/loginUser/fulfilled');
        expect(action.payload).toEqual(mockResponse.data);

        const state = store.getState().auth;
        expect(state.isLoading).toBe(false);
        expect(state.user).toEqual(testUser);
        expect(state.token).toBe('mock-token');
        expect(state.isAuthenticated).toBe(true);
        expect(state.error).toBeNull();
        expect(state.loginAttempts).toBe(0);
      });

      it('should handle login failure', async () => {
        const mockError = {
          response: {
            data: {
              message: 'Invalid credentials',
            },
          },
        };

        const { authService } = require('../../services/authService');
        authService.login.mockRejectedValue(mockError);

        const action = await store.dispatch(
          loginUser({ identifier: 'testuser', password: 'wrongpassword' })
        );

        expect(action.type).toBe('auth/loginUser/rejected');
        expect(action.payload).toBe('Invalid credentials');

        const state = store.getState().auth;
        expect(state.isLoading).toBe(false);
        expect(state.user).toBeNull();
        expect(state.token).toBeNull();
        expect(state.isAuthenticated).toBe(false);
        expect(state.error).toBe('Invalid credentials');
        expect(state.loginAttempts).toBe(1);
      });

      it('should set loading state during login', () => {
        const { authService } = require('../../services/authService');
        authService.login.mockImplementation(() => new Promise(() => {})); // Never resolves

        store.dispatch(loginUser({ identifier: 'testuser', password: 'password123' }));

        const state = store.getState().auth;
        expect(state.isLoading).toBe(true);
        expect(state.error).toBeNull();
      });
    });

    describe('registerUser', () => {
      it('should handle successful registration', async () => {
        const mockResponse = {
          data: {
            success: true,
            data: {
              user: testUser,
              token: 'mock-token',
            },
          },
        };

        const { authService } = require('../../services/authService');
        authService.register.mockResolvedValue(mockResponse);

        const userData = {
          username: 'newuser',
          email: '<EMAIL>',
          password: 'password123',
          phone: '+254700000000',
        };

        const action = await store.dispatch(registerUser(userData));

        expect(action.type).toBe('auth/registerUser/fulfilled');
        expect(action.payload).toEqual(mockResponse.data);

        const state = store.getState().auth;
        expect(state.isLoading).toBe(false);
        expect(state.user).toEqual(testUser);
        expect(state.token).toBe('mock-token');
        expect(state.isAuthenticated).toBe(true);
        expect(state.error).toBeNull();
      });

      it('should handle registration failure', async () => {
        const mockError = {
          response: {
            data: {
              message: 'Username already exists',
            },
          },
        };

        const { authService } = require('../../services/authService');
        authService.register.mockRejectedValue(mockError);

        const userData = {
          username: 'existinguser',
          email: '<EMAIL>',
          password: 'password123',
          phone: '+254700000000',
        };

        const action = await store.dispatch(registerUser(userData));

        expect(action.type).toBe('auth/registerUser/rejected');
        expect(action.payload).toBe('Username already exists');

        const state = store.getState().auth;
        expect(state.isLoading).toBe(false);
        expect(state.error).toBe('Username already exists');
      });
    });

    describe('loginWithBiometrics', () => {
      it('should handle successful biometric login', async () => {
        const mockResponse = {
          data: {
            success: true,
            data: {
              user: testUser,
              token: 'mock-token',
            },
          },
        };

        const { biometricService } = require('../../services/biometricService');
        const { authService } = require('../../services/authService');

        biometricService.isBiometricEnabled.mockResolvedValue(true);
        biometricService.getStoredIdentifier.mockResolvedValue('testuser');
        biometricService.authenticateForLogin.mockResolvedValue({ success: true, signature: 'mock-signature' });
        authService.loginWithBiometrics.mockResolvedValue(mockResponse);

        const action = await store.dispatch(loginWithBiometrics());

        expect(action.type).toBe('auth/loginWithBiometrics/fulfilled');
        expect(action.payload).toEqual(mockResponse.data);

        const state = store.getState().auth;
        expect(state.isLoading).toBe(false);
        expect(state.user).toEqual(testUser);
        expect(state.token).toBe('mock-token');
        expect(state.isAuthenticated).toBe(true);
        expect(state.error).toBeNull();
      });

      it('should handle biometric authentication failure', async () => {
        const { biometricService } = require('../../services/biometricService');

        biometricService.isBiometricEnabled.mockResolvedValue(true);
        biometricService.getStoredIdentifier.mockResolvedValue('testuser');
        biometricService.authenticateForLogin.mockResolvedValue({ success: false, error: 'Authentication failed' });

        const action = await store.dispatch(loginWithBiometrics());

        expect(action.type).toBe('auth/loginWithBiometrics/rejected');
        expect(action.payload).toBe('Authentication failed');

        const state = store.getState().auth;
        expect(state.isLoading).toBe(false);
        expect(state.error).toBe('Authentication failed');
      });

      it('should handle biometric not enabled', async () => {
        const { biometricService } = require('../../services/biometricService');

        biometricService.isBiometricEnabled.mockResolvedValue(false);

        const action = await store.dispatch(loginWithBiometrics());

        expect(action.type).toBe('auth/loginWithBiometrics/rejected');
        expect(action.payload).toBe('Biometric authentication not enabled');
      });
    });

    describe('enableBiometricAuth', () => {
      it('should handle successful biometric enable', async () => {
        const { biometricService } = require('../../services/biometricService');
        biometricService.enableBiometricAuth.mockResolvedValue(true);

        const action = await store.dispatch(enableBiometricAuth('testuser'));

        expect(action.type).toBe('auth/enableBiometricAuth/fulfilled');
        expect(action.payload).toBe(true);

        const state = store.getState().auth;
        expect(state.biometricEnabled).toBe(true);
        expect(state.error).toBeNull();
      });

      it('should handle biometric enable failure', async () => {
        const { biometricService } = require('../../services/biometricService');
        biometricService.enableBiometricAuth.mockResolvedValue(false);

        const action = await store.dispatch(enableBiometricAuth('testuser'));

        expect(action.type).toBe('auth/enableBiometricAuth/rejected');
        expect(action.payload).toBe('Failed to enable biometric authentication');
      });
    });

    describe('disableBiometricAuth', () => {
      it('should handle successful biometric disable', async () => {
        const { biometricService } = require('../../services/biometricService');
        biometricService.disableBiometricAuth.mockResolvedValue(true);

        const action = await store.dispatch(disableBiometricAuth());

        expect(action.type).toBe('auth/disableBiometricAuth/fulfilled');
        expect(action.payload).toBe(true);

        const state = store.getState().auth;
        expect(state.biometricEnabled).toBe(false);
        expect(state.error).toBeNull();
      });
    });
  });

  describe('selectors', () => {
    it('should select auth state', () => {
      const state = { auth: store.getState().auth };
      expect(selectAuth(state)).toEqual(state.auth);
    });

    it('should select user', () => {
      store.dispatch(updateUser(testUser));
      const state = { auth: store.getState().auth };
      expect(selectUser(state)).toEqual(testUser);
    });

    it('should select isAuthenticated', () => {
      const state = { auth: store.getState().auth };
      expect(selectIsAuthenticated(state)).toBe(false);

      // Simulate login
      store.dispatch({
        type: 'auth/loginUser/fulfilled',
        payload: { user: testUser, token: 'mock-token' },
      });

      const newState = { auth: store.getState().auth };
      expect(selectIsAuthenticated(newState)).toBe(true);
    });
  });
});
