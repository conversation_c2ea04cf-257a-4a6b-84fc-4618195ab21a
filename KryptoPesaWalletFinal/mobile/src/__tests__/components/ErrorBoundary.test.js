import React from 'react';
import { render } from '@testing-library/react-native';
import { Provider as PaperProvider } from 'react-native-paper';
import ErrorBoundary from '../../components/ErrorBoundary';
import { theme } from '../../utils/theme';

// Component that throws an error
const ThrowError = ({ shouldThrow }) => {
  if (shouldThrow) {
    throw new Error('Test error');
  }
  return <></>;
};

// Wrapper component for testing
const TestWrapper = ({ children }) => (
  <PaperProvider theme={theme}>
    {children}
  </PaperProvider>
);

describe('ErrorBoundary', () => {
  // Suppress console.error for these tests
  const originalError = console.error;
  beforeAll(() => {
    console.error = jest.fn();
  });

  afterAll(() => {
    console.error = originalError;
  });

  it('should render children when there is no error', () => {
    const { getByText } = render(
      <TestWrapper>
        <ErrorBoundary>
          <ThrowError shouldThrow={false} />
          <text>Test content</text>
        </ErrorBoundary>
      </TestWrapper>
    );

    expect(getByText('Test content')).toBeTruthy();
  });

  it('should render error UI when child component throws', () => {
    const { getByText } = render(
      <TestWrapper>
        <ErrorBoundary>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      </TestWrapper>
    );

    expect(getByText('Something went wrong')).toBeTruthy();
    expect(getByText('Try Again')).toBeTruthy();
    expect(getByText('Restart App')).toBeTruthy();
  });

  it('should render custom title and message', () => {
    const customTitle = 'Custom Error Title';
    const customMessage = 'Custom error message';

    const { getByText } = render(
      <TestWrapper>
        <ErrorBoundary title={customTitle} message={customMessage}>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      </TestWrapper>
    );

    expect(getByText(customTitle)).toBeTruthy();
    expect(getByText(customMessage)).toBeTruthy();
  });

  it('should show contact support button when enabled', () => {
    const { getByText } = render(
      <TestWrapper>
        <ErrorBoundary showContactSupport={true}>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      </TestWrapper>
    );

    expect(getByText('Contact Support')).toBeTruthy();
  });

  it('should not show contact support button when disabled', () => {
    const { queryByText } = render(
      <TestWrapper>
        <ErrorBoundary showContactSupport={false}>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      </TestWrapper>
    );

    expect(queryByText('Contact Support')).toBeNull();
  });

  it('should call onError callback when error occurs', () => {
    const onErrorMock = jest.fn();

    render(
      <TestWrapper>
        <ErrorBoundary onError={onErrorMock}>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      </TestWrapper>
    );

    expect(onErrorMock).toHaveBeenCalledWith(
      expect.any(Error),
      expect.objectContaining({
        componentStack: expect.any(String),
      })
    );
  });

  it('should call onContactSupport callback when contact support is pressed', () => {
    const onContactSupportMock = jest.fn();

    const { getByText } = render(
      <TestWrapper>
        <ErrorBoundary 
          showContactSupport={true} 
          onContactSupport={onContactSupportMock}
        >
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      </TestWrapper>
    );

    const contactButton = getByText('Contact Support');
    contactButton.props.onPress();

    expect(onContactSupportMock).toHaveBeenCalled();
  });

  it('should call onRestart callback when restart is pressed', () => {
    const onRestartMock = jest.fn();

    const { getByText } = render(
      <TestWrapper>
        <ErrorBoundary onRestart={onRestartMock}>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      </TestWrapper>
    );

    const restartButton = getByText('Restart App');
    restartButton.props.onPress();

    expect(onRestartMock).toHaveBeenCalled();
  });

  it('should reset error state when try again is pressed', () => {
    const TestComponent = () => {
      const [shouldThrow, setShouldThrow] = React.useState(true);

      return (
        <ErrorBoundary>
          <ThrowError shouldThrow={shouldThrow} />
          <text onPress={() => setShouldThrow(false)}>
            Normal content
          </text>
        </ErrorBoundary>
      );
    };

    const { getByText, queryByText } = render(
      <TestWrapper>
        <TestComponent />
      </TestWrapper>
    );

    // Error should be shown initially
    expect(getByText('Something went wrong')).toBeTruthy();

    // Press try again
    const tryAgainButton = getByText('Try Again');
    tryAgainButton.props.onPress();

    // Error should be cleared (component will re-render)
    // Note: In a real scenario, the component would need to handle state changes
    // to prevent the error from happening again
  });

  it('should show debug information in development mode', () => {
    const originalDev = __DEV__;
    // @ts-ignore
    __DEV__ = true;

    const { getByText } = render(
      <TestWrapper>
        <ErrorBoundary>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      </TestWrapper>
    );

    expect(getByText('Debug Information:')).toBeTruthy();

    // @ts-ignore
    __DEV__ = originalDev;
  });

  it('should not show debug information in production mode', () => {
    const originalDev = __DEV__;
    // @ts-ignore
    __DEV__ = false;

    const { queryByText } = render(
      <TestWrapper>
        <ErrorBoundary>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      </TestWrapper>
    );

    expect(queryByText('Debug Information:')).toBeNull();

    // @ts-ignore
    __DEV__ = originalDev;
  });

  it('should handle multiple errors gracefully', () => {
    const { getByText, rerender } = render(
      <TestWrapper>
        <ErrorBoundary>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      </TestWrapper>
    );

    expect(getByText('Something went wrong')).toBeTruthy();

    // Trigger another error by re-rendering
    rerender(
      <TestWrapper>
        <ErrorBoundary>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      </TestWrapper>
    );

    // Should still show error UI
    expect(getByText('Something went wrong')).toBeTruthy();
  });

  it('should have proper accessibility labels', () => {
    const { getByText } = render(
      <TestWrapper>
        <ErrorBoundary>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      </TestWrapper>
    );

    const tryAgainButton = getByText('Try Again');
    const restartButton = getByText('Restart App');

    expect(tryAgainButton).toBeTruthy();
    expect(restartButton).toBeTruthy();
  });
});
