import 'react-native-gesture-handler/jestSetup';

// Mock react-native modules
jest.mock('react-native/Libraries/Animated/NativeAnimatedHelper');

// Mock react-native-reanimated
jest.mock('react-native-reanimated', () => {
  const Reanimated = require('react-native-reanimated/mock');
  Reanimated.default.call = () => {};
  return Reanimated;
});

// Mock react-native-encrypted-storage
jest.mock('react-native-encrypted-storage', () => ({
  setItem: jest.fn(() => Promise.resolve()),
  getItem: jest.fn(() => Promise.resolve(null)),
  removeItem: jest.fn(() => Promise.resolve()),
  clear: jest.fn(() => Promise.resolve()),
}));

// Mock react-native-flash-message
jest.mock('react-native-flash-message', () => ({
  showMessage: jest.fn(),
  hideMessage: jest.fn(),
  default: () => null,
}));

// Mock react-native-vector-icons
jest.mock('react-native-vector-icons/MaterialCommunityIcons', () => 'Icon');

// Mock react-native-paper
jest.mock('react-native-paper', () => {
  const RealModule = jest.requireActual('react-native-paper');
  const MockedModule = {
    ...RealModule,
    Portal: ({ children }) => children,
  };
  return MockedModule;
});

// Mock navigation
jest.mock('@react-navigation/native', () => {
  const actualNav = jest.requireActual('@react-navigation/native');
  return {
    ...actualNav,
    useNavigation: () => ({
      navigate: jest.fn(),
      goBack: jest.fn(),
      reset: jest.fn(),
      setOptions: jest.fn(),
    }),
    useRoute: () => ({
      params: {},
    }),
    useFocusEffect: jest.fn(),
  };
});

// Mock expo modules
jest.mock('expo-constants', () => ({
  default: {
    expoConfig: {
      extra: {
        apiUrl: 'http://localhost:3000/api',
      },
    },
  },
}));

jest.mock('expo-application', () => ({
  nativeApplicationVersion: '1.0.0',
  nativeBuildVersion: '1',
}));

jest.mock('expo-device', () => ({
  brand: 'Apple',
  modelName: 'iPhone',
  osName: 'iOS',
  osVersion: '15.0',
}));

// Mock biometric service
jest.mock('../services/biometricService', () => ({
  biometricService: {
    initialize: jest.fn(() => Promise.resolve(true)),
    isBiometricAvailable: jest.fn(() => Promise.resolve(true)),
    isBiometricEnabled: jest.fn(() => Promise.resolve(false)),
    enableBiometricAuth: jest.fn(() => Promise.resolve(true)),
    disableBiometricAuth: jest.fn(() => Promise.resolve(true)),
    authenticateForLogin: jest.fn(() => Promise.resolve({ success: true })),
    getBiometricType: jest.fn(() => Promise.resolve('TouchID')),
    getBiometricTypeLabel: jest.fn(() => 'Touch ID'),
  },
}));

// Mock camera service
jest.mock('../services/cameraService', () => ({
  cameraService: {
    initialize: jest.fn(() => Promise.resolve(true)),
    capturePaymentProof: jest.fn(() => Promise.resolve({
      id: 'test-proof-id',
      fileName: 'test-proof.jpg',
      localPath: '/test/path/test-proof.jpg',
      size: 1024,
      timestamp: Date.now(),
    })),
    getPaymentProofs: jest.fn(() => Promise.resolve([])),
    deletePaymentProof: jest.fn(() => Promise.resolve(true)),
  },
}));

// Mock socket service
jest.mock('../services/socketService', () => ({
  socketService: {
    connect: jest.fn(),
    disconnect: jest.fn(),
    emit: jest.fn(),
    on: jest.fn(),
    off: jest.fn(),
    isConnected: jest.fn(() => false),
  },
}));

// Mock notification service
jest.mock('../services/notificationService', () => ({
  notificationService: {
    initialize: jest.fn(() => Promise.resolve()),
    requestPermissions: jest.fn(() => Promise.resolve(true)),
    getToken: jest.fn(() => Promise.resolve('mock-fcm-token')),
    scheduleLocalNotification: jest.fn(),
  },
}));

// Mock QR scanner
jest.mock('expo-barcode-scanner', () => ({
  BarCodeScanner: {
    requestPermissionsAsync: jest.fn(() => Promise.resolve({ status: 'granted' })),
    Constants: {
      BarCodeType: {
        qr: 'qr',
      },
    },
  },
}));

// Mock image picker
jest.mock('react-native-image-picker', () => ({
  launchCamera: jest.fn((options, callback) => {
    callback({
      assets: [{
        uri: 'file://test-image.jpg',
        type: 'image/jpeg',
        fileSize: 1024,
        width: 800,
        height: 600,
      }],
    });
  }),
  launchImageLibrary: jest.fn((options, callback) => {
    callback({
      assets: [{
        uri: 'file://test-image.jpg',
        type: 'image/jpeg',
        fileSize: 1024,
        width: 800,
        height: 600,
      }],
    });
  }),
}));

// Mock file system
jest.mock('react-native-fs', () => ({
  DocumentDirectoryPath: '/mock/documents',
  exists: jest.fn(() => Promise.resolve(true)),
  mkdir: jest.fn(() => Promise.resolve()),
  copyFile: jest.fn(() => Promise.resolve()),
  unlink: jest.fn(() => Promise.resolve()),
  readDir: jest.fn(() => Promise.resolve([])),
  stat: jest.fn(() => Promise.resolve({ size: 1024 })),
}));

// Mock clipboard
jest.mock('@react-native-clipboard/clipboard', () => ({
  setString: jest.fn(),
  getString: jest.fn(() => Promise.resolve('')),
}));

// Mock haptics
jest.mock('expo-haptics', () => ({
  impactAsync: jest.fn(),
  notificationAsync: jest.fn(),
  selectionAsync: jest.fn(),
}));

// Global test utilities
global.mockStore = {
  getState: jest.fn(() => ({
    auth: {
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,
    },
    wallet: {
      address: null,
      balance: '0',
      transactions: [],
      isLoading: false,
      error: null,
    },
    trade: {
      trades: [],
      currentTrade: null,
      isLoading: false,
      error: null,
    },
  })),
  dispatch: jest.fn(),
  subscribe: jest.fn(),
};

global.mockNavigation = {
  navigate: jest.fn(),
  goBack: jest.fn(),
  reset: jest.fn(),
  setOptions: jest.fn(),
  addListener: jest.fn(),
  removeListener: jest.fn(),
};

global.mockRoute = {
  params: {},
  key: 'test-route',
  name: 'TestScreen',
};

// Test data
global.testUser = {
  _id: 'test-user-id',
  username: 'testuser',
  email: '<EMAIL>',
  profile: {
    firstName: 'Test',
    lastName: 'User',
  },
  verification: {
    email: { verified: true },
    phone: { verified: true },
  },
  reputation: {
    score: 85,
    totalTrades: 10,
  },
};

global.testOffer = {
  _id: 'test-offer-id',
  offerId: 'OFF123456',
  type: 'sell',
  cryptocurrency: {
    symbol: 'USDT',
    amount: '100',
    network: 'TRC20',
  },
  fiat: {
    currency: 'KES',
    amount: 13000,
    paymentMethods: ['M-Pesa'],
  },
  creator: global.testUser,
  status: 'active',
};

global.testTrade = {
  _id: 'test-trade-id',
  tradeId: 'TRD123456',
  seller: global.testUser,
  buyer: global.testUser,
  offer: global.testOffer,
  cryptocurrency: {
    symbol: 'USDT',
    amount: '50',
    network: 'TRC20',
  },
  fiat: {
    currency: 'KES',
    amount: 6500,
  },
  status: 'created',
  createdAt: new Date().toISOString(),
};

// Silence console warnings in tests
global.console = {
  ...console,
  warn: jest.fn(),
  error: jest.fn(),
};
