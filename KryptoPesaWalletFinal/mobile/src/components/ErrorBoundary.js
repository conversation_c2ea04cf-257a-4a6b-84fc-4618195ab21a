import React from 'react';
import { View, StyleSheet } from 'react-native';
import { <PERSON>, <PERSON><PERSON>, Card } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { theme } from '../utils/theme';

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { 
      hasError: false, 
      error: null, 
      errorInfo: null 
    };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    // Log error details
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    
    this.setState({
      error: error,
      errorInfo: errorInfo
    });

    // Report error to crash analytics service
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // In production, report to crash analytics
    if (!__DEV__) {
      this.reportError(error, errorInfo);
    }
  }

  reportError = (error, errorInfo) => {
    // Report to crash analytics service (Firebase Crashlytics, Sentry, etc.)
    try {
      // Example: Firebase Crashlytics
      // crashlytics().recordError(error);
      
      // Example: Sentry
      // Sentry.captureException(error, {
      //   contexts: {
      //     react: {
      //       componentStack: errorInfo.componentStack
      //     }
      //   }
      // });
      
      console.log('Error reported to analytics service');
    } catch (reportingError) {
      console.error('Failed to report error:', reportingError);
    }
  };

  handleRetry = () => {
    this.setState({ 
      hasError: false, 
      error: null, 
      errorInfo: null 
    });
  };

  handleRestart = () => {
    // In React Native, you might want to restart the app
    // This could be implemented with a restart library or navigation reset
    if (this.props.onRestart) {
      this.props.onRestart();
    } else {
      // Default behavior: reset to initial state
      this.handleRetry();
    }
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      return (
        <View style={styles.container}>
          <Card style={styles.errorCard}>
            <Card.Content style={styles.errorContent}>
              <Icon 
                name="alert-circle" 
                size={64} 
                color={theme.colors.error} 
                style={styles.errorIcon}
              />
              
              <Text style={styles.errorTitle}>
                {this.props.title || 'Something went wrong'}
              </Text>
              
              <Text style={styles.errorMessage}>
                {this.props.message || 
                 'An unexpected error occurred. Please try again or restart the app.'}
              </Text>

              {__DEV__ && this.state.error && (
                <View style={styles.debugInfo}>
                  <Text style={styles.debugTitle}>Debug Information:</Text>
                  <Text style={styles.debugText}>
                    {this.state.error.toString()}
                  </Text>
                  {this.state.errorInfo && (
                    <Text style={styles.debugText}>
                      {this.state.errorInfo.componentStack}
                    </Text>
                  )}
                </View>
              )}

              <View style={styles.buttonContainer}>
                <Button
                  mode="contained"
                  onPress={this.handleRetry}
                  style={styles.retryButton}
                  icon="refresh"
                >
                  Try Again
                </Button>
                
                <Button
                  mode="outlined"
                  onPress={this.handleRestart}
                  style={styles.restartButton}
                  icon="restart"
                >
                  Restart App
                </Button>
              </View>

              {this.props.showContactSupport && (
                <Button
                  mode="text"
                  onPress={() => {
                    // Navigate to support or open email
                    if (this.props.onContactSupport) {
                      this.props.onContactSupport();
                    }
                  }}
                  style={styles.supportButton}
                  icon="help-circle"
                >
                  Contact Support
                </Button>
              )}
            </Card.Content>
          </Card>
        </View>
      );
    }

    // Render children normally
    return this.props.children;
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.background,
    padding: theme.spacing.md,
  },
  errorCard: {
    width: '100%',
    maxWidth: 400,
  },
  errorContent: {
    alignItems: 'center',
    paddingVertical: theme.spacing.xl,
  },
  errorIcon: {
    marginBottom: theme.spacing.lg,
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: theme.spacing.md,
    color: theme.colors.error,
  },
  errorMessage: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: theme.spacing.lg,
    color: theme.colors.onSurfaceVariant,
    lineHeight: 24,
  },
  debugInfo: {
    backgroundColor: theme.colors.surfaceVariant,
    padding: theme.spacing.md,
    borderRadius: theme.roundness,
    marginBottom: theme.spacing.lg,
    width: '100%',
  },
  debugTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: theme.spacing.sm,
    color: theme.colors.error,
  },
  debugText: {
    fontSize: 12,
    fontFamily: 'monospace',
    color: theme.colors.onSurfaceVariant,
    marginBottom: theme.spacing.xs,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    marginBottom: theme.spacing.md,
  },
  retryButton: {
    flex: 1,
    marginRight: theme.spacing.sm,
  },
  restartButton: {
    flex: 1,
    marginLeft: theme.spacing.sm,
  },
  supportButton: {
    marginTop: theme.spacing.sm,
  },
});

export default ErrorBoundary;
