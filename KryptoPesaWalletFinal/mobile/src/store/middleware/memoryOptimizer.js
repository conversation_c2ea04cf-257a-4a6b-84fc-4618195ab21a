/**
 * Memory optimization middleware for Redux store
 * Prevents memory leaks and optimizes performance
 */

const MEMORY_LIMITS = {
  CHAT_MESSAGES_LIMIT: 100,
  TRANSACTION_HISTORY_LIMIT: 200,
  TRADE_HISTORY_LIMIT: 50,
  OFFER_CACHE_LIMIT: 100,
  IMAGE_CACHE_LIMIT: 20,
};

const CLEANUP_ACTIONS = [
  'chat/addMessage',
  'wallet/addTransaction',
  'trade/addTrade',
  'offer/addOffer',
];

const memoryOptimizerMiddleware = (store) => (next) => (action) => {
  const result = next(action);
  
  // Only run cleanup for specific actions to avoid performance impact
  if (CLEANUP_ACTIONS.some(cleanupAction => action.type.includes(cleanupAction))) {
    const state = store.getState();
    
    // Schedule cleanup on next tick to avoid blocking UI
    setTimeout(() => {
      cleanupMemory(store, state);
    }, 0);
  }
  
  return result;
};

const cleanupMemory = (store, state) => {
  const cleanupActions = [];
  
  // Cleanup chat messages
  Object.keys(state.chat.conversations || {}).forEach(tradeId => {
    const messages = state.chat.conversations[tradeId]?.messages || [];
    if (messages.length > MEMORY_LIMITS.CHAT_MESSAGES_LIMIT) {
      cleanupActions.push({
        type: 'chat/trimMessages',
        payload: {
          tradeId,
          keepLast: MEMORY_LIMITS.CHAT_MESSAGES_LIMIT
        }
      });
    }
  });
  
  // Cleanup transaction history
  const transactions = state.wallet.transactionHistory?.transactions || [];
  if (transactions.length > MEMORY_LIMITS.TRANSACTION_HISTORY_LIMIT) {
    cleanupActions.push({
      type: 'wallet/trimTransactionHistory',
      payload: {
        keepLast: MEMORY_LIMITS.TRANSACTION_HISTORY_LIMIT
      }
    });
  }
  
  // Cleanup trade history
  const trades = state.trade.trades || [];
  if (trades.length > MEMORY_LIMITS.TRADE_HISTORY_LIMIT) {
    cleanupActions.push({
      type: 'trade/trimTradeHistory',
      payload: {
        keepLast: MEMORY_LIMITS.TRADE_HISTORY_LIMIT
      }
    });
  }
  
  // Cleanup offer cache
  const offers = state.offer.offers || [];
  if (offers.length > MEMORY_LIMITS.OFFER_CACHE_LIMIT) {
    cleanupActions.push({
      type: 'offer/trimOfferCache',
      payload: {
        keepLast: MEMORY_LIMITS.OFFER_CACHE_LIMIT
      }
    });
  }
  
  // Dispatch cleanup actions
  cleanupActions.forEach(action => {
    store.dispatch(action);
  });
  
  // Force garbage collection if available (development only)
  if (__DEV__ && global.gc) {
    global.gc();
  }
};

/**
 * Image cache cleanup utility
 */
export const cleanupImageCache = async () => {
  try {
    const RNFS = require('react-native-fs');
    const cacheDir = `${RNFS.CachesDirectoryPath}/images`;
    
    // Check if cache directory exists
    const exists = await RNFS.exists(cacheDir);
    if (!exists) return;
    
    // Get all cached images
    const files = await RNFS.readDir(cacheDir);
    
    // Sort by modification time (oldest first)
    const sortedFiles = files
      .filter(file => file.isFile())
      .sort((a, b) => new Date(a.mtime) - new Date(b.mtime));
    
    // Remove oldest files if cache exceeds limit
    if (sortedFiles.length > MEMORY_LIMITS.IMAGE_CACHE_LIMIT) {
      const filesToDelete = sortedFiles.slice(0, sortedFiles.length - MEMORY_LIMITS.IMAGE_CACHE_LIMIT);
      
      for (const file of filesToDelete) {
        try {
          await RNFS.unlink(file.path);
        } catch (error) {
          console.warn('Failed to delete cached image:', file.path, error);
        }
      }
      
      console.log(`Cleaned up ${filesToDelete.length} cached images`);
    }
  } catch (error) {
    console.warn('Image cache cleanup failed:', error);
  }
};

/**
 * Memory monitoring utility
 */
export const getMemoryUsage = () => {
  if (__DEV__ && global.performance && global.performance.memory) {
    return {
      used: Math.round(global.performance.memory.usedJSHeapSize / 1024 / 1024),
      total: Math.round(global.performance.memory.totalJSHeapSize / 1024 / 1024),
      limit: Math.round(global.performance.memory.jsHeapSizeLimit / 1024 / 1024),
    };
  }
  return null;
};

/**
 * Log memory usage (development only)
 */
export const logMemoryUsage = (context = '') => {
  if (__DEV__) {
    const usage = getMemoryUsage();
    if (usage) {
      console.log(`Memory Usage ${context}:`, usage);
      
      // Warn if memory usage is high
      if (usage.used > usage.limit * 0.8) {
        console.warn('High memory usage detected!', usage);
      }
    }
  }
};

/**
 * Periodic memory cleanup
 */
export const startPeriodicCleanup = (store) => {
  // Run cleanup every 5 minutes
  const interval = setInterval(() => {
    const state = store.getState();
    cleanupMemory(store, state);
    cleanupImageCache();
    logMemoryUsage('Periodic Cleanup');
  }, 5 * 60 * 1000);
  
  return () => clearInterval(interval);
};

export default memoryOptimizerMiddleware;
