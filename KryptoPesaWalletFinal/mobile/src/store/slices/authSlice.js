import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { authService } from '../../services/authService';
import { socketService } from '../../services/socketService';
import { biometricService } from '../../services/biometricService';
import { showMessage } from 'react-native-flash-message';

// Async thunks
export const loginUser = createAsyncThunk(
  'auth/login',
  async ({ identifier, password }, { rejectWithValue }) => {
    try {
      const response = await authService.login(identifier, password);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Login failed');
    }
  }
);

export const registerUser = createAsyncThunk(
  'auth/register',
  async (userData, { rejectWithValue }) => {
    try {
      const response = await authService.register(userData);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Registration failed');
    }
  }
);

export const getCurrentUser = createAsyncThunk(
  'auth/getCurrentUser',
  async (_, { rejectWithValue }) => {
    try {
      const response = await authService.getCurrentUser();
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to get user');
    }
  }
);

export const logoutUser = createAsyncThunk(
  'auth/logout',
  async (_, { dispatch }) => {
    try {
      await authService.logout();
      // Clear other slices data on logout
      dispatch({ type: 'wallet/clearWallet' });
      dispatch({ type: 'trade/clearTrades' });
      dispatch({ type: 'offer/clearOffers' });
      dispatch({ type: 'chat/clearChats' });
    } catch (error) {
      // Continue with logout even if API call fails
    }
  }
);

export const loginWithBiometrics = createAsyncThunk(
  'auth/loginWithBiometrics',
  async (_, { rejectWithValue }) => {
    try {
      // Check if biometric authentication is available and enabled
      const isEnabled = await biometricService.isBiometricEnabled();
      if (!isEnabled) {
        return rejectWithValue('Biometric authentication not enabled');
      }

      // Get stored user identifier
      const userIdentifier = await biometricService.getStoredIdentifier();
      if (!userIdentifier) {
        return rejectWithValue('No user identifier found for biometric login');
      }

      // Authenticate with biometrics
      const biometricResult = await biometricService.authenticateForLogin();
      if (!biometricResult.success) {
        return rejectWithValue(biometricResult.error || 'Biometric authentication failed');
      }

      // Login with biometric token
      const response = await authService.loginWithBiometrics(userIdentifier, biometricResult.signature);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Biometric login failed');
    }
  }
);

export const enableBiometricAuth = createAsyncThunk(
  'auth/enableBiometricAuth',
  async (userIdentifier, { rejectWithValue }) => {
    try {
      const result = await biometricService.enableBiometricAuth(userIdentifier);
      if (result) {
        showMessage({
          message: 'Biometric authentication enabled successfully',
          type: 'success',
        });
        return true;
      } else {
        return rejectWithValue('Failed to enable biometric authentication');
      }
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to enable biometric authentication');
    }
  }
);

export const disableBiometricAuth = createAsyncThunk(
  'auth/disableBiometricAuth',
  async (_, { rejectWithValue }) => {
    try {
      const result = await biometricService.disableBiometricAuth();
      if (result) {
        showMessage({
          message: 'Biometric authentication disabled',
          type: 'info',
        });
        return true;
      } else {
        return rejectWithValue('Failed to disable biometric authentication');
      }
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to disable biometric authentication');
    }
  }
);

const initialState = {
  user: null,
  token: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
  loginAttempts: 0,
  lastLoginAttempt: null,
  biometricEnabled: false,
  biometricAvailable: false,
};

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    updateUser: (state, action) => {
      state.user = { ...state.user, ...action.payload };
    },
    incrementLoginAttempts: (state) => {
      state.loginAttempts += 1;
      state.lastLoginAttempt = Date.now();
    },
    resetLoginAttempts: (state) => {
      state.loginAttempts = 0;
      state.lastLoginAttempt = null;
    },
    setBiometricAvailable: (state, action) => {
      state.biometricAvailable = action.payload;
    },
    setBiometricEnabled: (state, action) => {
      state.biometricEnabled = action.payload;
    },
    logout: (state) => {
      state.user = null;
      state.token = null;
      state.isAuthenticated = false;
      state.loginAttempts = 0;
      state.lastLoginAttempt = null;
      state.error = null;

      // Disconnect socket
      socketService.disconnect();
    },
  },
  extraReducers: (builder) => {
    builder
      // Login
      .addCase(loginUser.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(loginUser.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload.user;
        state.token = action.payload.token;
        state.isAuthenticated = true;
        state.loginAttempts = 0;
        state.lastLoginAttempt = null;

        // Connect to socket with token
        socketService.connect(action.payload.token);

        showMessage({
          message: 'Welcome back!',
          description: `Hello ${action.payload.user.profile.firstName}`,
          type: 'success',
        });
      })
      .addCase(loginUser.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
        state.loginAttempts += 1;
        state.lastLoginAttempt = Date.now();
        
        showMessage({
          message: 'Login Failed',
          description: action.payload,
          type: 'danger',
        });
      })
      
      // Register
      .addCase(registerUser.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(registerUser.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload.user;
        state.token = action.payload.token;
        state.isAuthenticated = true;
        
        showMessage({
          message: 'Welcome to KryptoPesa!',
          description: 'Your account has been created successfully',
          type: 'success',
        });
      })
      .addCase(registerUser.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
        
        showMessage({
          message: 'Registration Failed',
          description: action.payload,
          type: 'danger',
        });
      })
      
      // Get current user
      .addCase(getCurrentUser.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getCurrentUser.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload.user;
        state.isAuthenticated = true;
      })
      .addCase(getCurrentUser.rejected, (state) => {
        state.isLoading = false;
        state.user = null;
        state.token = null;
        state.isAuthenticated = false;
      })
      
      // Logout
      .addCase(logoutUser.fulfilled, (state) => {
        state.user = null;
        state.token = null;
        state.isAuthenticated = false;
        state.isLoading = false;
        state.error = null;

        showMessage({
          message: 'Logged out successfully',
          type: 'info',
        });
      })

      // Biometric Login
      .addCase(loginWithBiometrics.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(loginWithBiometrics.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload.user;
        state.token = action.payload.token;
        state.isAuthenticated = true;
        state.loginAttempts = 0;
        state.lastLoginAttempt = null;

        // Connect socket
        socketService.connect(action.payload.token);

        showMessage({
          message: 'Biometric login successful',
          type: 'success',
        });
      })
      .addCase(loginWithBiometrics.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
        state.loginAttempts += 1;
        state.lastLoginAttempt = Date.now();
      })

      // Enable Biometric Auth
      .addCase(enableBiometricAuth.fulfilled, (state) => {
        state.biometricEnabled = true;
      })
      .addCase(enableBiometricAuth.rejected, (state, action) => {
        state.error = action.payload;
      })

      // Disable Biometric Auth
      .addCase(disableBiometricAuth.fulfilled, (state) => {
        state.biometricEnabled = false;
      })
      .addCase(disableBiometricAuth.rejected, (state, action) => {
        state.error = action.payload;
      });
  },
});

export const {
  clearError,
  updateUser,
  incrementLoginAttempts,
  resetLoginAttempts,
  setBiometricAvailable,
  setBiometricEnabled
} = authSlice.actions;

export default authSlice.reducer;

// Selectors
export const selectAuth = (state) => state.auth;
export const selectUser = (state) => state.auth.user;
export const selectIsAuthenticated = (state) => state.auth.isAuthenticated;
export const selectAuthLoading = (state) => state.auth.isLoading;
export const selectAuthError = (state) => state.auth.error;
export const selectBiometricEnabled = (state) => state.auth.biometricEnabled;
export const selectBiometricAvailable = (state) => state.auth.biometricAvailable;
