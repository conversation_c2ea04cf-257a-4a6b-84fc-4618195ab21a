import { configureStore } from '@reduxjs/toolkit';
import { persistStore, persistReducer } from 'redux-persist';
import EncryptedStorage from 'react-native-encrypted-storage';
import { combineReducers } from '@reduxjs/toolkit';

// Import reducers
import authReducer from './slices/authSlice';
import walletReducer from './slices/walletSlice';
import tradeReducer from './slices/tradeSlice';
import offerReducer from './slices/offerSlice';
import chatReducer from './slices/chatSlice';
import appReducer from './slices/appSlice';

// Import memory optimizer middleware
import memoryOptimizerMiddleware from './middleware/memoryOptimizer';

// Persist config
const persistConfig = {
  key: 'root',
  storage: EncryptedStorage,
  whitelist: ['auth', 'wallet', 'app'], // Only persist these reducers
  blacklist: ['trade', 'offer', 'chat'], // Don't persist real-time data
};

// Root reducer
const rootReducer = combineReducers({
  auth: authReducer,
  wallet: walletReducer,
  trade: tradeReducer,
  offer: offerReducer,
  chat: chatReducer,
  app: appReducer,
});

// Persisted reducer
const persistedReducer = persistReducer(persistConfig, rootReducer);

// Configure store
export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [
          'persist/PERSIST',
          'persist/REHYDRATE',
          'persist/PAUSE',
          'persist/PURGE',
          'persist/REGISTER',
        ],
      },
      // Disable immutability check in production for performance
      immutableCheck: __DEV__,
    }).concat(memoryOptimizerMiddleware),
  devTools: __DEV__,
});

export const persistor = persistStore(store);

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
