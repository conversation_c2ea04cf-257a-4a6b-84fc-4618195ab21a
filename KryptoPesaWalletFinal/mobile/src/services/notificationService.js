import messaging from '@react-native-firebase/messaging';
import notifee, { AndroidImportance, AndroidVisibility } from '@notifee/react-native';
import { Platform, Alert, Linking } from 'react-native';
import EncryptedStorage from 'react-native-encrypted-storage';

class NotificationService {
  constructor() {
    this.isInitialized = false;
    this.fcmToken = null;
    this.notificationListeners = [];
    this.unsubscribeTokenRefresh = null;
    this.unsubscribeForegroundMessages = null;
    this.unsubscribeBackgroundMessages = null;
  }

  async initialize() {
    try {
      console.log('Initializing notification service...');
      
      // Request permissions
      const hasPermission = await this.requestPermissions();
      if (!hasPermission) {
        console.log('Notification permissions not granted');
        return false;
      }

      // Initialize Notifee
      await this.initializeNotifee();

      // Get FCM token
      await this.getFCMToken();

      // Set up message handlers
      this.setupMessageHandlers();

      // Set up token refresh listener
      this.setupTokenRefreshListener();

      this.isInitialized = true;
      console.log('Notification service initialized successfully');
      return true;
    } catch (error) {
      console.error('Error initializing notification service:', error);
      return false;
    }
  }

  async requestPermissions() {
    try {
      if (Platform.OS === 'ios') {
        const authStatus = await messaging().requestPermission();
        const enabled =
          authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
          authStatus === messaging.AuthorizationStatus.PROVISIONAL;
        
        if (!enabled) {
          Alert.alert(
            'Notifications Disabled',
            'Please enable notifications in Settings to receive trade updates and messages.',
            [
              { text: 'Cancel', style: 'cancel' },
              { text: 'Open Settings', onPress: () => Linking.openSettings() },
            ]
          );
        }
        
        return enabled;
      } else {
        // Android permissions are handled by Notifee
        const settings = await notifee.requestPermission();
        return settings.authorizationStatus === 1; // AUTHORIZED
      }
    } catch (error) {
      console.error('Error requesting notification permissions:', error);
      return false;
    }
  }

  async initializeNotifee() {
    try {
      // Create notification channels for Android
      if (Platform.OS === 'android') {
        await notifee.createChannels([
          {
            id: 'trade_updates',
            name: 'Trade Updates',
            description: 'Notifications for trade status changes',
            importance: AndroidImportance.HIGH,
            visibility: AndroidVisibility.PUBLIC,
          },
          {
            id: 'new_messages',
            name: 'New Messages',
            description: 'Notifications for new chat messages',
            importance: AndroidImportance.HIGH,
            visibility: AndroidVisibility.PUBLIC,
          },
          {
            id: 'system_alerts',
            name: 'System Alerts',
            description: 'Important system notifications',
            importance: AndroidImportance.HIGH,
            visibility: AndroidVisibility.PUBLIC,
          },
        ]);
      }
    } catch (error) {
      console.error('Error initializing Notifee:', error);
    }
  }

  async getFCMToken() {
    try {
      // Check if we have a cached token
      const cachedToken = await EncryptedStorage.getItem('fcm_token');
      
      // Get current token
      const token = await messaging().getToken();
      
      if (token) {
        this.fcmToken = token;
        
        // Update cached token if different
        if (cachedToken !== token) {
          await EncryptedStorage.setItem('fcm_token', token);
          console.log('FCM token updated:', token);
        }
        
        return token;
      } else {
        console.log('No FCM token available');
        return null;
      }
    } catch (error) {
      console.error('Error getting FCM token:', error);
      return null;
    }
  }

  setupTokenRefreshListener() {
    this.unsubscribeTokenRefresh = messaging().onTokenRefresh(async (token) => {
      console.log('FCM token refreshed:', token);
      this.fcmToken = token;
      await EncryptedStorage.setItem('fcm_token', token);
      
      // TODO: Send updated token to backend
      // await this.updateTokenOnServer(token);
    });
  }

  setupMessageHandlers() {
    // Foreground message handler
    this.unsubscribeForegroundMessages = messaging().onMessage(async (remoteMessage) => {
      console.log('Foreground message received:', remoteMessage);
      
      // Show local notification for foreground messages
      await this.showLocalNotification({
        title: remoteMessage.notification?.title || 'KryptoPesa',
        body: remoteMessage.notification?.body || 'New notification',
        data: remoteMessage.data || {},
      });
    });

    // Background message handler
    messaging().setBackgroundMessageHandler(async (remoteMessage) => {
      console.log('Background message received:', remoteMessage);
      
      // Process background message
      await this.handleBackgroundMessage(remoteMessage);
    });

    // Notification opened handler
    messaging().onNotificationOpenedApp((remoteMessage) => {
      console.log('Notification opened app:', remoteMessage);
      this.handleNotificationOpen(remoteMessage);
    });

    // Check if app was opened from a notification
    messaging()
      .getInitialNotification()
      .then((remoteMessage) => {
        if (remoteMessage) {
          console.log('App opened from notification:', remoteMessage);
          this.handleNotificationOpen(remoteMessage);
        }
      });
  }

  async showLocalNotification({ title, body, data = {}, channelId = 'system_alerts' }) {
    try {
      await notifee.displayNotification({
        title,
        body,
        data,
        android: {
          channelId,
          importance: AndroidImportance.HIGH,
          pressAction: {
            id: 'default',
          },
        },
        ios: {
          sound: 'default',
          badge: 1,
        },
      });
    } catch (error) {
      console.error('Error showing local notification:', error);
    }
  }

  async handleBackgroundMessage(remoteMessage) {
    try {
      // Process different types of background messages
      const { type } = remoteMessage.data || {};
      
      switch (type) {
        case 'trade_update':
          await this.handleTradeUpdateNotification(remoteMessage);
          break;
        case 'new_message':
          await this.handleNewMessageNotification(remoteMessage);
          break;
        case 'system_alert':
          await this.handleSystemAlertNotification(remoteMessage);
          break;
        default:
          console.log('Unknown background message type:', type);
      }
    } catch (error) {
      console.error('Error handling background message:', error);
    }
  }

  async handleTradeUpdateNotification(remoteMessage) {
    const { tradeId, status } = remoteMessage.data || {};
    
    await this.showLocalNotification({
      title: 'Trade Update',
      body: `Trade ${tradeId} status: ${status}`,
      data: { type: 'trade_update', tradeId, status },
      channelId: 'trade_updates',
    });
  }

  async handleNewMessageNotification(remoteMessage) {
    const { tradeId, senderName } = remoteMessage.data || {};
    
    await this.showLocalNotification({
      title: `New message from ${senderName}`,
      body: remoteMessage.notification?.body || 'You have a new message',
      data: { type: 'new_message', tradeId },
      channelId: 'new_messages',
    });
  }

  async handleSystemAlertNotification(remoteMessage) {
    await this.showLocalNotification({
      title: remoteMessage.notification?.title || 'System Alert',
      body: remoteMessage.notification?.body || 'Important system notification',
      data: remoteMessage.data || {},
      channelId: 'system_alerts',
    });
  }

  handleNotificationOpen(remoteMessage) {
    const { type, tradeId } = remoteMessage.data || {};
    
    // Notify listeners about notification open
    this.notificationListeners.forEach(listener => {
      listener({
        type: 'notification_opened',
        data: { type, tradeId, remoteMessage },
      });
    });
  }

  addNotificationListener(callback) {
    this.notificationListeners.push(callback);
    console.log('Notification listener added');
  }

  removeNotificationListener(callback) {
    this.notificationListeners = this.notificationListeners.filter(cb => cb !== callback);
    console.log('Notification listener removed');
  }

  async areNotificationsEnabled() {
    try {
      const settings = await notifee.getNotificationSettings();
      return settings.authorizationStatus === 1; // AUTHORIZED
    } catch (error) {
      console.error('Error checking notification settings:', error);
      return false;
    }
  }

  async openNotificationSettings() {
    try {
      await notifee.openNotificationSettings();
    } catch (error) {
      console.error('Error opening notification settings:', error);
    }
  }

  async clearAllNotifications() {
    try {
      await notifee.cancelAllNotifications();
    } catch (error) {
      console.error('Error clearing notifications:', error);
    }
  }

  async getBadgeCount() {
    try {
      if (Platform.OS === 'ios') {
        return await notifee.getBadgeCount();
      }
      return 0;
    } catch (error) {
      console.error('Error getting badge count:', error);
      return 0;
    }
  }

  async setBadgeCount(count) {
    try {
      if (Platform.OS === 'ios') {
        await notifee.setBadgeCount(count);
      }
    } catch (error) {
      console.error('Error setting badge count:', error);
    }
  }

  cleanup() {
    if (this.unsubscribeTokenRefresh) {
      this.unsubscribeTokenRefresh();
    }
    if (this.unsubscribeForegroundMessages) {
      this.unsubscribeForegroundMessages();
    }
    this.notificationListeners = [];
  }
}

// Create singleton instance
export const notificationService = new NotificationService();

// Export notification types for reference
export const NOTIFICATION_TYPES = {
  TRADE_UPDATE: 'trade_update',
  NEW_MESSAGE: 'new_message',
  SYSTEM_ALERT: 'system_alert',
};

export default notificationService;
