import { launchC<PERSON>ra, launchImageLibrary } from 'react-native-image-picker';
import { Alert, Platform, PermissionsAndroid } from 'react-native';
import RNFS from 'react-native-fs';

class CameraService {
  constructor() {
    this.isInitialized = false;
  }

  async initialize() {
    try {
      if (Platform.OS === 'android') {
        await this.requestAndroidPermissions();
      }
      this.isInitialized = true;
      return true;
    } catch (error) {
      console.error('Error initializing camera service:', error);
      return false;
    }
  }

  async requestAndroidPermissions() {
    try {
      const cameraPermission = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.CAMERA,
        {
          title: 'Camera Permission',
          message: '<PERSON><PERSON>ptoPes<PERSON> needs access to your camera to take photos for payment verification.',
          buttonNeutral: 'Ask Me Later',
          buttonNegative: 'Cancel',
          buttonPositive: 'OK',
        }
      );

      const storagePermission = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
        {
          title: 'Storage Permission',
          message: '<PERSON><PERSON>pto<PERSON><PERSON><PERSON> needs access to your storage to save payment proof images.',
          buttonNeutral: 'Ask Me Later',
          buttonNegative: 'Cancel',
          buttonPositive: 'OK',
        }
      );

      return (
        cameraPermission === PermissionsAndroid.RESULTS.GRANTED &&
        storagePermission === PermissionsAndroid.RESULTS.GRANTED
      );
    } catch (error) {
      console.error('Error requesting Android permissions:', error);
      return false;
    }
  }

  getImagePickerOptions(quality = 0.8, maxWidth = 1024, maxHeight = 1024) {
    return {
      mediaType: 'photo',
      includeBase64: false,
      maxHeight,
      maxWidth,
      quality,
      storageOptions: {
        skipBackup: true,
        path: 'images',
      },
    };
  }

  async capturePaymentProof(tradeId, options = {}) {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      const imageOptions = {
        ...this.getImagePickerOptions(),
        ...options,
      };

      return new Promise((resolve, reject) => {
        Alert.alert(
          'Payment Proof',
          'How would you like to add your payment proof?',
          [
            {
              text: 'Take Photo',
              onPress: () => {
                launchCamera(imageOptions, (response) => {
                  this.handleImageResponse(response, tradeId, resolve, reject);
                });
              },
            },
            {
              text: 'Choose from Gallery',
              onPress: () => {
                launchImageLibrary(imageOptions, (response) => {
                  this.handleImageResponse(response, tradeId, resolve, reject);
                });
              },
            },
            {
              text: 'Cancel',
              style: 'cancel',
              onPress: () => resolve(null),
            },
          ]
        );
      });
    } catch (error) {
      console.error('Error capturing payment proof:', error);
      throw error;
    }
  }

  async handleImageResponse(response, tradeId, resolve, reject) {
    try {
      if (response.didCancel) {
        resolve(null);
        return;
      }

      if (response.errorMessage) {
        reject(new Error(response.errorMessage));
        return;
      }

      if (response.assets && response.assets.length > 0) {
        const asset = response.assets[0];
        
        // Validate image
        if (!this.validateImage(asset)) {
          reject(new Error('Invalid image format or size'));
          return;
        }

        // Process and save image
        const processedImage = await this.processImage(asset, tradeId);
        resolve(processedImage);
      } else {
        resolve(null);
      }
    } catch (error) {
      reject(error);
    }
  }

  validateImage(asset) {
    // Check file size (max 10MB)
    const maxSize = 10 * 1024 * 1024;
    if (asset.fileSize > maxSize) {
      Alert.alert('Error', 'Image size must be less than 10MB');
      return false;
    }

    // Check file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];
    if (!allowedTypes.includes(asset.type)) {
      Alert.alert('Error', 'Only JPEG and PNG images are allowed');
      return false;
    }

    return true;
  }

  async processImage(asset, tradeId) {
    try {
      const timestamp = Date.now();
      const fileName = `payment_proof_${tradeId}_${timestamp}.jpg`;
      const documentsPath = RNFS.DocumentDirectoryPath;
      const imagePath = `${documentsPath}/payment_proofs/${fileName}`;

      // Create directory if it doesn't exist
      const dirPath = `${documentsPath}/payment_proofs`;
      const dirExists = await RNFS.exists(dirPath);
      if (!dirExists) {
        await RNFS.mkdir(dirPath);
      }

      // Copy image to app directory
      await RNFS.copyFile(asset.uri, imagePath);

      // Get image info
      const imageInfo = await RNFS.stat(imagePath);

      return {
        id: `proof_${timestamp}`,
        fileName,
        localPath: imagePath,
        originalUri: asset.uri,
        size: imageInfo.size,
        type: asset.type,
        width: asset.width,
        height: asset.height,
        timestamp,
        tradeId,
      };
    } catch (error) {
      console.error('Error processing image:', error);
      throw error;
    }
  }

  async captureMultipleProofs(tradeId, maxImages = 3) {
    try {
      const proofs = [];
      
      for (let i = 0; i < maxImages; i++) {
        const shouldContinue = await new Promise((resolve) => {
          const message = i === 0 
            ? 'Add payment proof image'
            : `Add another payment proof image? (${i}/${maxImages})`;
          
          Alert.alert(
            'Payment Proof',
            message,
            [
              { text: 'Add Image', onPress: () => resolve(true) },
              { text: i === 0 ? 'Cancel' : 'Done', onPress: () => resolve(false) },
            ]
          );
        });

        if (!shouldContinue) break;

        const proof = await this.capturePaymentProof(tradeId);
        if (proof) {
          proofs.push(proof);
        }
      }

      return proofs;
    } catch (error) {
      console.error('Error capturing multiple proofs:', error);
      throw error;
    }
  }

  async deletePaymentProof(proof) {
    try {
      if (proof.localPath && await RNFS.exists(proof.localPath)) {
        await RNFS.unlink(proof.localPath);
      }
      return true;
    } catch (error) {
      console.error('Error deleting payment proof:', error);
      return false;
    }
  }

  async getPaymentProofs(tradeId) {
    try {
      const documentsPath = RNFS.DocumentDirectoryPath;
      const proofsDir = `${documentsPath}/payment_proofs`;
      
      const dirExists = await RNFS.exists(proofsDir);
      if (!dirExists) {
        return [];
      }

      const files = await RNFS.readDir(proofsDir);
      const tradeProofs = files.filter(file => 
        file.name.includes(`payment_proof_${tradeId}_`)
      );

      return tradeProofs.map(file => ({
        id: file.name.replace('.jpg', ''),
        fileName: file.name,
        localPath: file.path,
        size: file.size,
        timestamp: parseInt(file.name.split('_').pop().replace('.jpg', '')),
        tradeId,
      }));
    } catch (error) {
      console.error('Error getting payment proofs:', error);
      return [];
    }
  }

  async uploadPaymentProof(proof, uploadUrl) {
    try {
      const formData = new FormData();
      formData.append('file', {
        uri: proof.localPath,
        type: proof.type || 'image/jpeg',
        name: proof.fileName,
      });
      formData.append('tradeId', proof.tradeId);
      formData.append('timestamp', proof.timestamp.toString());

      const response = await fetch(uploadUrl, {
        method: 'POST',
        body: formData,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      if (response.ok) {
        const result = await response.json();
        return result;
      } else {
        throw new Error('Upload failed');
      }
    } catch (error) {
      console.error('Error uploading payment proof:', error);
      throw error;
    }
  }

  async clearAllPaymentProofs() {
    try {
      const documentsPath = RNFS.DocumentDirectoryPath;
      const proofsDir = `${documentsPath}/payment_proofs`;
      
      const dirExists = await RNFS.exists(proofsDir);
      if (dirExists) {
        await RNFS.unlink(proofsDir);
      }
      
      return true;
    } catch (error) {
      console.error('Error clearing payment proofs:', error);
      return false;
    }
  }
}

// Create singleton instance
export const cameraService = new CameraService();

export default cameraService;
