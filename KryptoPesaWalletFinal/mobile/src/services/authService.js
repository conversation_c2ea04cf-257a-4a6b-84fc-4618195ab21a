import EncryptedStorage from 'react-native-encrypted-storage';
import { apiService } from './apiService';

class AuthService {
  async login(identifier, password) {
    try {
      const response = await apiService.auth.login({ identifier, password });
      
      if (response.data.success && response.data.data.token) {
        // Store token securely
        await EncryptedStorage.setItem('auth_token', response.data.data.token);
        
        // Store user data
        await EncryptedStorage.setItem('user_data', JSON.stringify(response.data.data.user));
      }
      
      return response;
    } catch (error) {
      throw error;
    }
  }
  
  async register(userData) {
    try {
      const response = await apiService.auth.register(userData);

      if (response.data.success && response.data.data.token) {
        // Store token securely
        await EncryptedStorage.setItem('auth_token', response.data.data.token);

        // Store user data
        await EncryptedStorage.setItem('user_data', JSON.stringify(response.data.data.user));
      }

      return response;
    } catch (error) {
      throw error;
    }
  }

  async loginWithBiometrics(userIdentifier, signature) {
    try {
      const response = await apiService.auth.loginWithBiometrics({
        userIdentifier,
        signature
      });

      if (response.data.success && response.data.data.token) {
        // Store token securely
        await EncryptedStorage.setItem('auth_token', response.data.data.token);

        // Store user data
        await EncryptedStorage.setItem('user_data', JSON.stringify(response.data.data.user));
      }

      return response;
    } catch (error) {
      throw error;
    }
  }

  async getCurrentUser() {
    try {
      const response = await apiService.auth.getCurrentUser();
      
      if (response.data.success) {
        // Update stored user data
        await EncryptedStorage.setItem('user_data', JSON.stringify(response.data.data.user));
      }
      
      return response;
    } catch (error) {
      throw error;
    }
  }
  
  async logout() {
    try {
      // Call logout endpoint
      await apiService.auth.logout();
    } catch (error) {
      // Continue with logout even if API call fails
      console.log('Logout API call failed:', error);
    } finally {
      // Clear stored data
      await this.clearAuthData();
    }
  }
  
  async clearAuthData() {
    try {
      await EncryptedStorage.removeItem('auth_token');
      await EncryptedStorage.removeItem('user_data');
      await EncryptedStorage.removeItem('wallet_data');
    } catch (error) {
      console.log('Error clearing auth data:', error);
    }
  }
  
  async getStoredToken() {
    try {
      return await EncryptedStorage.getItem('auth_token');
    } catch (error) {
      return null;
    }
  }
  
  async getStoredUser() {
    try {
      const userData = await EncryptedStorage.getItem('user_data');
      return userData ? JSON.parse(userData) : null;
    } catch (error) {
      return null;
    }
  }
  
  async isAuthenticated() {
    try {
      const token = await this.getStoredToken();
      return !!token;
    } catch (error) {
      return false;
    }
  }
}

export const authService = new AuthService();
