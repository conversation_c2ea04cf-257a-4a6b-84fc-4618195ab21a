import io from 'socket.io-client';
import { store } from '../store';
import { updateTradeStatus, addTradeMessage } from '../store/slices/tradeSlice';
import { addChatMessage, updateMessageStatus, setTypingStatus } from '../store/slices/chatSlice';
import { showMessage } from 'react-native-flash-message';
import { selectUser } from '../store/slices/authSlice';
import EncryptedStorage from 'react-native-encrypted-storage';

class SocketService {
  constructor() {
    this.socket = null;
    this.isConnected = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 1000;
    this.connectionListeners = [];
    this.joinedRooms = new Set();
  }

  // Initialize socket connection
  async connect(token) {
    if (this.socket?.connected) {
      console.log('Socket already connected');
      return;
    }

    // Get token if not provided
    if (!token) {
      try {
        token = await EncryptedStorage.getItem('auth_token');
      } catch (error) {
        console.error('Error getting auth token:', error);
        return;
      }
    }

    if (!token) {
      console.log('No auth token available for socket connection');
      return;
    }

    const socketUrl = __DEV__
      ? 'http://localhost:3000'
      : 'https://api.kryptopesa.com';

    console.log('Connecting to socket server:', socketUrl);

    try {
      this.socket = io(socketUrl, {
        auth: {
          token: token
        },
        transports: ['websocket', 'polling'],
        timeout: 20000,
        reconnection: true,
        reconnectionDelay: 1000,
        reconnectionDelayMax: 5000,
        maxReconnectionAttempts: this.maxReconnectAttempts,
      });

      this.setupEventListeners();
    } catch (error) {
      console.error('Error creating socket connection:', error);
    }
  }

  // Disconnect socket
  disconnect() {
    if (this.socket) {
      console.log('Disconnecting socket');
      this.socket.disconnect();
      this.socket = null;
      this.isConnected = false;
      this.joinedRooms.clear();
      this.notifyConnectionListeners('disconnected');
    }
  }

  // Setup all socket event listeners
  setupEventListeners() {
    if (!this.socket) return;

    // Connection events
    this.socket.on('connect', () => {
      console.log('Socket connected:', this.socket.id);
      this.isConnected = true;
      this.reconnectAttempts = 0;
      this.notifyConnectionListeners('connected');
      
      // Rejoin previously joined rooms
      this.rejoinRooms();
    });

    this.socket.on('disconnect', (reason) => {
      console.log('Socket disconnected:', reason);
      this.isConnected = false;
      this.notifyConnectionListeners('disconnected');
      
      // Attempt reconnection for certain disconnect reasons
      if (reason === 'io server disconnect') {
        // Server initiated disconnect, don't reconnect automatically
        return;
      }
      
      this.handleReconnection();
    });

    this.socket.on('connect_error', (error) => {
      console.log('Socket connection error:', error);
      this.notifyConnectionListeners('error', error);
      this.handleReconnection();
    });

    // Trade events
    this.socket.on('trade_update', (data) => {
      console.log('Trade update received:', data);
      store.dispatch(updateTradeStatus({
        tradeId: data.tradeId,
        status: data.status,
        timestamp: data.timestamp,
        details: data.details
      }));

      // Show notification for important trade updates
      if (['funded', 'payment_sent', 'completed', 'disputed'].includes(data.status)) {
        showMessage({
          message: 'Trade Update',
          description: `Trade ${data.tradeId} status: ${data.status}`,
          type: 'info',
          duration: 4000,
        });
      }
    });

    // Chat events
    this.socket.on('new_message', (data) => {
      console.log('New message received:', data);
      const currentUser = selectUser(store.getState());
      
      // Don't show notification for own messages
      if (data.message.sender._id !== currentUser?._id) {
        store.dispatch(addChatMessage({
          tradeId: data.tradeId,
          message: data.message
        }));

        showMessage({
          message: `New message from ${data.sender.username}`,
          description: data.message.content,
          type: 'info',
          duration: 3000,
        });
      }
    });

    this.socket.on('message_read', (data) => {
      console.log('Message read receipt:', data);
      store.dispatch(updateMessageStatus({
        tradeId: data.tradeId,
        messageIds: data.messageIds,
        status: 'read'
      }));
    });

    this.socket.on('typing_start', (data) => {
      console.log('User started typing:', data);
      store.dispatch(setTypingStatus({
        tradeId: data.tradeId,
        userId: data.userId,
        username: data.username,
        isTyping: true
      }));
    });

    this.socket.on('typing_stop', (data) => {
      console.log('User stopped typing:', data);
      store.dispatch(setTypingStatus({
        tradeId: data.tradeId,
        userId: data.userId,
        isTyping: false
      }));
    });

    // System events
    this.socket.on('notification', (data) => {
      console.log('System notification:', data);
      showMessage({
        message: data.title || 'Notification',
        description: data.message,
        type: data.type || 'info',
        duration: 5000,
      });
    });

    // Error events
    this.socket.on('error', (error) => {
      console.log('Socket error:', error);
      showMessage({
        message: 'Connection Error',
        description: error.message || 'Something went wrong',
        type: 'danger',
      });
    });
  }

  // Handle reconnection logic
  handleReconnection() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.log('Max reconnection attempts reached');
      this.notifyConnectionListeners('failed');
      return;
    }

    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1); // Exponential backoff
    
    console.log(`Attempting reconnection ${this.reconnectAttempts}/${this.maxReconnectAttempts} in ${delay}ms`);
    this.notifyConnectionListeners('reconnecting', { attempt: this.reconnectAttempts, delay });

    setTimeout(() => {
      if (!this.isConnected && this.socket) {
        this.socket.connect();
      }
    }, delay);
  }

  // Rejoin rooms after reconnection
  rejoinRooms() {
    this.joinedRooms.forEach(room => {
      console.log('Rejoining room:', room);
      this.socket.emit('join_room', room);
    });
  }

  // Join a trade room for real-time updates
  joinTradeRoom(tradeId) {
    if (!this.socket || !this.isConnected) {
      console.log('Socket not connected, cannot join trade room');
      return;
    }

    const room = `trade:${tradeId}`;
    console.log('Joining trade room:', room);
    
    this.socket.emit('join_trade', { tradeId });
    this.joinedRooms.add(room);
  }

  // Leave a trade room
  leaveTradeRoom(tradeId) {
    if (!this.socket) return;

    const room = `trade:${tradeId}`;
    console.log('Leaving trade room:', room);
    
    this.socket.emit('leave_trade', { tradeId });
    this.joinedRooms.delete(room);
  }

  // Send a chat message
  sendMessage(tradeId, content, type = 'text', attachments = []) {
    if (!this.socket || !this.isConnected) {
      throw new Error('Not connected to server');
    }

    console.log('Sending message:', { tradeId, content, type });
    
    this.socket.emit('send_message', {
      tradeId,
      content,
      type,
      attachments
    });
  }

  // Send typing indicator
  sendTypingStart(tradeId) {
    if (!this.socket || !this.isConnected) return;
    
    this.socket.emit('typing_start', { tradeId });
  }

  sendTypingStop(tradeId) {
    if (!this.socket || !this.isConnected) return;
    
    this.socket.emit('typing_stop', { tradeId });
  }

  // Mark messages as read
  markMessagesAsRead(tradeId, messageIds) {
    if (!this.socket || !this.isConnected) return;
    
    this.socket.emit('mark_read', { tradeId, messageIds });
  }

  // Connection state management
  addConnectionListener(callback) {
    this.connectionListeners.push(callback);
  }

  removeConnectionListener(callback) {
    this.connectionListeners = this.connectionListeners.filter(cb => cb !== callback);
  }

  notifyConnectionListeners(status, data = null) {
    this.connectionListeners.forEach(callback => {
      try {
        callback(status, data);
      } catch (error) {
        console.log('Error in connection listener:', error);
      }
    });
  }

  // Utility methods
  isSocketConnected() {
    return this.socket?.connected || false;
  }

  getConnectionState() {
    if (!this.socket) return 'disconnected';
    if (this.socket.connected) return 'connected';
    if (this.socket.connecting) return 'connecting';
    return 'disconnected';
  }

  // Force reconnection
  forceReconnect() {
    if (this.socket) {
      this.reconnectAttempts = 0;
      this.socket.disconnect();
      this.socket.connect();
    }
  }
}

// Create singleton instance
export const socketService = new SocketService();

// Export connection states for UI components
export const CONNECTION_STATES = {
  DISCONNECTED: 'disconnected',
  CONNECTING: 'connecting',
  CONNECTED: 'connected',
  RECONNECTING: 'reconnecting',
  FAILED: 'failed',
  ERROR: 'error'
};
