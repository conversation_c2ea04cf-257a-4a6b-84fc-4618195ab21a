import ReactNativeBiometrics from 'react-native-biometrics';
import { Alert, Platform } from 'react-native';
import EncryptedStorage from 'react-native-encrypted-storage';

class BiometricService {
  constructor() {
    this.rnBiometrics = new ReactNativeBiometrics({
      allowDeviceCredentials: true
    });
    this.isInitialized = false;
    this.biometricType = null;
  }

  async initialize() {
    try {
      const { available, biometryType } = await this.rnBiometrics.isSensorAvailable();
      
      if (available) {
        this.biometricType = biometryType;
        this.isInitialized = true;
        console.log('Biometric authentication available:', biometryType);
        return true;
      } else {
        console.log('Biometric authentication not available');
        return false;
      }
    } catch (error) {
      console.error('Error initializing biometric service:', error);
      return false;
    }
  }

  async isBiometricAvailable() {
    if (!this.isInitialized) {
      await this.initialize();
    }
    return this.isInitialized;
  }

  async getBiometricType() {
    if (!this.isInitialized) {
      await this.initialize();
    }
    return this.biometricType;
  }

  getBiometricTypeLabel() {
    switch (this.biometricType) {
      case ReactNativeBiometrics.TouchID:
        return 'Touch ID';
      case ReactNativeBiometrics.FaceID:
        return 'Face ID';
      case ReactNativeBiometrics.Biometrics:
        return Platform.OS === 'android' ? 'Fingerprint' : 'Biometrics';
      default:
        return 'Biometric Authentication';
    }
  }

  async createBiometricKey() {
    try {
      const { available } = await this.rnBiometrics.isSensorAvailable();
      if (!available) {
        throw new Error('Biometric authentication not available');
      }

      const { keysExist } = await this.rnBiometrics.biometricKeysExist();
      
      if (!keysExist) {
        const { publicKey } = await this.rnBiometrics.createKeys();
        await EncryptedStorage.setItem('biometric_public_key', publicKey);
        return publicKey;
      } else {
        const publicKey = await EncryptedStorage.getItem('biometric_public_key');
        return publicKey;
      }
    } catch (error) {
      console.error('Error creating biometric key:', error);
      throw error;
    }
  }

  async deleteBiometricKey() {
    try {
      await this.rnBiometrics.deleteKeys();
      await EncryptedStorage.removeItem('biometric_public_key');
      await EncryptedStorage.removeItem('biometric_enabled');
      return true;
    } catch (error) {
      console.error('Error deleting biometric key:', error);
      return false;
    }
  }

  async enableBiometricAuth(userIdentifier = null) {
    try {
      const available = await this.isBiometricAvailable();
      if (!available) {
        Alert.alert(
          'Biometric Authentication Unavailable',
          'Your device does not support biometric authentication or it is not set up.',
          [{ text: 'OK' }]
        );
        return false;
      }

      // Create biometric key
      await this.createBiometricKey();

      // Test biometric authentication
      const result = await this.authenticateWithBiometrics('Enable biometric authentication for KryptoPesa?');

      if (result.success) {
        await EncryptedStorage.setItem('biometric_enabled', 'true');

        // Store user identifier for biometric login
        if (userIdentifier) {
          await EncryptedStorage.setItem('biometric_user_identifier', userIdentifier);
        }

        return true;
      } else {
        return false;
      }
    } catch (error) {
      console.error('Error enabling biometric auth:', error);
      Alert.alert('Error', 'Failed to enable biometric authentication. Please try again.');
      return false;
    }
  }

  async disableBiometricAuth() {
    try {
      await this.deleteBiometricKey();
      await EncryptedStorage.removeItem('biometric_user_identifier');
      return true;
    } catch (error) {
      console.error('Error disabling biometric auth:', error);
      return false;
    }
  }

  async getStoredIdentifier() {
    try {
      return await EncryptedStorage.getItem('biometric_user_identifier');
    } catch (error) {
      console.error('Error getting stored identifier:', error);
      return null;
    }
  }

  async isBiometricEnabled() {
    try {
      const enabled = await EncryptedStorage.getItem('biometric_enabled');
      const { keysExist } = await this.rnBiometrics.biometricKeysExist();
      return enabled === 'true' && keysExist;
    } catch (error) {
      return false;
    }
  }

  async authenticateWithBiometrics(promptMessage = 'Authenticate to continue') {
    try {
      const available = await this.isBiometricAvailable();
      if (!available) {
        return { success: false, error: 'Biometric authentication not available' };
      }

      const enabled = await this.isBiometricEnabled();
      if (!enabled) {
        return { success: false, error: 'Biometric authentication not enabled' };
      }

      const { success, signature } = await this.rnBiometrics.createSignature({
        promptMessage,
        payload: Date.now().toString(),
      });

      if (success) {
        return { success: true, signature };
      } else {
        return { success: false, error: 'Authentication failed' };
      }
    } catch (error) {
      console.error('Biometric authentication error:', error);
      
      if (error.message.includes('UserCancel')) {
        return { success: false, error: 'Authentication cancelled by user' };
      } else if (error.message.includes('UserFallback')) {
        return { success: false, error: 'User chose to use password instead' };
      } else if (error.message.includes('SystemCancel')) {
        return { success: false, error: 'Authentication cancelled by system' };
      } else if (error.message.includes('BiometryNotAvailable')) {
        return { success: false, error: 'Biometric authentication not available' };
      } else if (error.message.includes('BiometryNotEnrolled')) {
        return { success: false, error: 'No biometric credentials enrolled' };
      } else {
        return { success: false, error: 'Authentication failed' };
      }
    }
  }

  async authenticateForLogin() {
    return this.authenticateWithBiometrics('Authenticate to sign in to KryptoPesa');
  }

  async authenticateForTransaction(amount, currency) {
    const message = `Authenticate to send ${amount} ${currency}`;
    return this.authenticateWithBiometrics(message);
  }

  async authenticateForTradeAction(action) {
    const message = `Authenticate to ${action}`;
    return this.authenticateWithBiometrics(message);
  }

  async showBiometricPrompt(title, subtitle, description, negativeButtonText = 'Cancel') {
    try {
      const { success } = await this.rnBiometrics.simplePrompt({
        promptMessage: title,
        fallbackPromptMessage: subtitle,
      });

      return { success };
    } catch (error) {
      console.error('Biometric prompt error:', error);
      return { success: false, error: error.message };
    }
  }
}

// Create singleton instance
export const biometricService = new BiometricService();

// Export biometric types for reference
export const BIOMETRIC_TYPES = {
  TOUCH_ID: ReactNativeBiometrics.TouchID,
  FACE_ID: ReactNativeBiometrics.FaceID,
  BIOMETRICS: ReactNativeBiometrics.Biometrics,
};

export default biometricService;
