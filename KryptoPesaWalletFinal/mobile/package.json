{"name": "kryptopesa-mobile", "version": "1.0.0", "description": "KryptoPesa Mobile App - P2P Cryptocurrency Trading", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "test": "jest", "lint": "eslint . --ext .js,.jsx,.ts,.tsx"}, "dependencies": {"@react-native-firebase/app": "^18.3.0", "@react-native-firebase/messaging": "^18.3.0", "@notifee/react-native": "^7.8.0", "@react-navigation/bottom-tabs": "^6.5.8", "@react-navigation/drawer": "^6.6.3", "@react-navigation/native": "^6.1.7", "@react-navigation/stack": "^6.3.17", "@reduxjs/toolkit": "^1.9.5", "axios": "^1.4.0", "bip39": "^3.1.0", "ethers": "^5.7.2", "expo": "~49.0.0", "expo-status-bar": "^2.2.3", "lodash": "^4.17.21", "moment": "^2.29.4", "react": "18.2.0", "react-dom": "^18.3.1", "react-hook-form": "^7.45.2", "react-native": "0.72.10", "react-native-animatable": "^1.3.3", "react-native-biometrics": "^3.0.1", "react-native-camera": "^4.2.1", "react-native-chart-kit": "^6.12.0", "react-native-document-picker": "^9.0.1", "react-native-elements": "^3.4.3", "react-native-encrypted-storage": "^4.0.3", "react-native-flash-message": "^0.4.2", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "~2.12.0", "react-native-image-picker": "^5.6.0", "react-native-keychain": "^8.1.3", "react-native-linear-gradient": "^2.8.1", "react-native-modal": "^13.0.1", "react-native-paper": "^5.9.1", "react-native-push-notification": "^8.1.1", "react-native-qrcode-svg": "^6.2.0", "react-native-reanimated": "~3.3.0", "react-native-safe-area-context": "4.6.3", "react-native-screens": "~3.22.0", "react-native-skeleton-placeholder": "^5.2.4", "react-native-super-grid": "^4.4.4", "react-native-svg": "13.9.0", "react-native-swipe-gestures": "^1.0.5", "react-native-tab-view": "^3.5.2", "react-native-vector-icons": "^10.0.0", "react-redux": "^8.1.2", "redux-persist": "^6.0.0", "socket.io-client": "^4.7.2"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/eslint-config": "^0.72.2", "@react-native/metro-config": "^0.72.9", "@tsconfig/react-native": "^3.0.0", "@types/react": "^18.0.24", "@types/react-test-renderer": "^18.0.0", "@testing-library/react-native": "^12.3.0", "@testing-library/jest-native": "^5.4.3", "babel-jest": "^29.2.1", "babel-loader": "^9.1.0", "eslint": "^8.19.0", "html-webpack-plugin": "^5.5.0", "jest": "^29.2.1", "metro-react-native-babel-preset": "0.76.7", "prettier": "^2.4.1", "react-native-web": "^0.19.0", "react-test-renderer": "18.2.0", "typescript": "4.8.4", "url-loader": "^4.1.1", "webpack": "^5.88.0", "webpack-cli": "^5.1.0", "webpack-dev-server": "^4.15.0"}, "jest": {"preset": "react-native", "setupFilesAfterEnv": ["<rootDir>/src/__tests__/setup.js"], "testMatch": ["**/__tests__/**/*.test.js", "**/__tests__/**/*.test.jsx"], "collectCoverageFrom": ["src/**/*.{js,jsx}", "!src/__tests__/**", "!src/navigation/**", "!**/node_modules/**"], "transformIgnorePatterns": ["node_modules/(?!(react-native|@react-native|react-native-vector-icons|react-native-paper|react-native-reanimated|react-native-gesture-handler|@react-navigation)/)"]}, "engines": {"node": ">=18.0.0"}}