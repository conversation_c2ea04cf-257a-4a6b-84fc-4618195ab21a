import { by, device, element, expect, waitFor } from 'detox';

describe('Trading Workflow E2E Tests', () => {
  beforeAll(async () => {
    await device.launchApp();
  });

  beforeEach(async () => {
    await device.reloadReactNative();
  });

  describe('Complete Trading Flow', () => {
    it('should complete a full buy trade workflow', async () => {
      // Login as buyer
      await element(by.id('login-email-input')).typeText('<EMAIL>');
      await element(by.id('login-password-input')).typeText('password123');
      await element(by.id('login-button')).tap();

      // Wait for home screen
      await waitFor(element(by.id('home-screen')))
        .toBeVisible()
        .withTimeout(5000);

      // Navigate to offers
      await element(by.id('offers-tab')).tap();

      // Find and select a sell offer
      await waitFor(element(by.id('offer-list')))
        .toBeVisible()
        .withTimeout(3000);

      await element(by.id('offer-item-0')).tap();

      // Create trade from offer
      await element(by.id('create-trade-button')).tap();
      await element(by.id('trade-amount-input')).typeText('100');
      await element(by.id('confirm-trade-button')).tap();

      // Wait for trade creation
      await waitFor(element(by.id('trade-details-screen')))
        .toBeVisible()
        .withTimeout(5000);

      // Verify trade status is pending
      await expect(element(by.id('trade-status-chip'))).toHaveText('PENDING');

      // Wait for seller to fund escrow (simulated)
      await device.sendUserNotification({
        trigger: {
          type: 'push',
        },
        title: 'Trade Update',
        subtitle: 'Escrow funded',
        body: 'The seller has funded the escrow',
        badge: 1,
        payload: {
          type: 'trade_update',
          tradeId: 'test-trade-123',
          status: 'funded',
        },
      });

      // Verify status updated to funded
      await waitFor(element(by.text('FUNDED')))
        .toBeVisible()
        .withTimeout(10000);

      // Mark payment as sent
      await element(by.id('mark-payment-sent-button')).tap();

      // Add payment proof
      await element(by.id('add-payment-proof-button')).tap();
      await element(by.text('Choose from Gallery')).tap();

      // Confirm payment sent
      await element(by.id('confirm-payment-sent-button')).tap();

      // Verify status updated to payment sent
      await waitFor(element(by.text('PAYMENT SENT')))
        .toBeVisible()
        .withTimeout(5000);

      // Wait for seller to confirm payment (simulated)
      await device.sendUserNotification({
        trigger: {
          type: 'push',
        },
        title: 'Trade Update',
        subtitle: 'Payment confirmed',
        body: 'The seller has confirmed your payment',
        badge: 1,
        payload: {
          type: 'trade_update',
          tradeId: 'test-trade-123',
          status: 'payment_confirmed',
        },
      });

      // Verify status updated to payment confirmed
      await waitFor(element(by.text('PAYMENT CONFIRMED')))
        .toBeVisible()
        .withTimeout(10000);

      // Complete trade
      await element(by.id('complete-trade-button')).tap();

      // Verify trade completed
      await waitFor(element(by.text('COMPLETED')))
        .toBeVisible()
        .withTimeout(5000);

      // Verify success message
      await expect(element(by.text('Trade completed successfully!')))
        .toBeVisible();
    });

    it('should handle trade dispute workflow', async () => {
      // Login as buyer
      await element(by.id('login-email-input')).typeText('<EMAIL>');
      await element(by.id('login-password-input')).typeText('password123');
      await element(by.id('login-button')).tap();

      // Navigate to active trade
      await element(by.id('trades-tab')).tap();
      await element(by.id('active-trade-item-0')).tap();

      // Open dispute
      await element(by.id('open-dispute-button')).tap();

      // Confirm dispute
      await element(by.text('Open Dispute')).tap();

      // Verify dispute status
      await waitFor(element(by.text('DISPUTED')))
        .toBeVisible()
        .withTimeout(5000);

      // Verify dispute message
      await expect(element(by.text('Dispute opened successfully')))
        .toBeVisible();
    });
  });

  describe('Real-time Features', () => {
    it('should receive and display real-time chat messages', async () => {
      // Login and navigate to chat
      await element(by.id('login-email-input')).typeText('<EMAIL>');
      await element(by.id('login-password-input')).typeText('password123');
      await element(by.id('login-button')).tap();

      await element(by.id('trades-tab')).tap();
      await element(by.id('trade-item-0')).tap();
      await element(by.id('chat-button')).tap();

      // Send a message
      await element(by.id('message-input')).typeText('Hello, is the payment confirmed?');
      await element(by.id('send-message-button')).tap();

      // Verify message appears
      await waitFor(element(by.text('Hello, is the payment confirmed?')))
        .toBeVisible()
        .withTimeout(3000);

      // Simulate incoming message
      await device.sendUserNotification({
        trigger: {
          type: 'push',
        },
        title: 'New Message',
        subtitle: 'From: seller123',
        body: 'Yes, payment has been confirmed',
        badge: 1,
        payload: {
          type: 'new_message',
          tradeId: 'test-trade-123',
          message: 'Yes, payment has been confirmed',
          sender: 'seller123',
        },
      });

      // Verify incoming message appears
      await waitFor(element(by.text('Yes, payment has been confirmed')))
        .toBeVisible()
        .withTimeout(5000);
    });

    it('should handle WebSocket connection and reconnection', async () => {
      // Login
      await element(by.id('login-email-input')).typeText('<EMAIL>');
      await element(by.id('login-password-input')).typeText('password123');
      await element(by.id('login-button')).tap();

      // Verify connection status
      await waitFor(element(by.id('connection-status-connected')))
        .toBeVisible()
        .withTimeout(10000);

      // Simulate network disconnection
      await device.setURLBlacklist(['*']);

      // Verify disconnection status
      await waitFor(element(by.id('connection-status-disconnected')))
        .toBeVisible()
        .withTimeout(5000);

      // Restore network
      await device.setURLBlacklist([]);

      // Verify reconnection
      await waitFor(element(by.id('connection-status-connected')))
        .toBeVisible()
        .withTimeout(10000);
    });
  });

  describe('Wallet Operations', () => {
    it('should send cryptocurrency successfully', async () => {
      // Login
      await element(by.id('login-email-input')).typeText('<EMAIL>');
      await element(by.id('login-password-input')).typeText('password123');
      await element(by.id('login-button')).tap();

      // Navigate to wallet
      await element(by.id('wallet-tab')).tap();

      // Tap send button
      await element(by.id('send-button')).tap();

      // Fill send form
      await element(by.id('recipient-input')).typeText('******************************************');
      await element(by.id('amount-input')).typeText('0.1');
      await element(by.id('token-selector')).tap();
      await element(by.text('USDT')).tap();

      // Confirm transaction
      await element(by.id('confirm-send-button')).tap();

      // Verify success message
      await waitFor(element(by.text('Transaction sent successfully')))
        .toBeVisible()
        .withTimeout(10000);
    });

    it('should scan QR code for receiving', async () => {
      // Login
      await element(by.id('login-email-input')).typeText('<EMAIL>');
      await element(by.id('login-password-input')).typeText('password123');
      await element(by.id('login-button')).tap();

      // Navigate to wallet
      await element(by.id('wallet-tab')).tap();

      // Tap receive button
      await element(by.id('receive-button')).tap();

      // Verify QR code is displayed
      await expect(element(by.id('qr-code-display'))).toBeVisible();

      // Verify wallet address is shown
      await expect(element(by.id('wallet-address-text'))).toBeVisible();

      // Test copy address functionality
      await element(by.id('copy-address-button')).tap();

      // Verify copy success message
      await expect(element(by.text('Address copied to clipboard')))
        .toBeVisible();
    });
  });

  describe('Authentication Features', () => {
    it('should enable and use biometric authentication', async () => {
      // Login with password first
      await element(by.id('login-email-input')).typeText('<EMAIL>');
      await element(by.id('login-password-input')).typeText('password123');
      await element(by.id('login-button')).tap();

      // Navigate to profile
      await element(by.id('profile-tab')).tap();

      // Navigate to biometric settings
      await element(by.id('biometric-settings-item')).tap();

      // Enable biometric authentication
      await element(by.id('biometric-toggle')).tap();

      // Simulate biometric authentication
      await device.setBiometricEnrollment(true);

      // Logout
      await element(by.id('logout-button')).tap();
      await element(by.text('Sign Out')).tap();

      // Try biometric login
      await element(by.id('biometric-login-button')).tap();

      // Simulate successful biometric authentication
      await device.matchBiometric();

      // Verify successful login
      await waitFor(element(by.id('home-screen')))
        .toBeVisible()
        .withTimeout(5000);
    });
  });

  describe('Error Handling', () => {
    it('should handle network errors gracefully', async () => {
      // Disable network
      await device.setURLBlacklist(['*']);

      // Try to login
      await element(by.id('login-email-input')).typeText('<EMAIL>');
      await element(by.id('login-password-input')).typeText('password123');
      await element(by.id('login-button')).tap();

      // Verify error message
      await waitFor(element(by.text('Network error. Please check your connection.')))
        .toBeVisible()
        .withTimeout(5000);

      // Restore network
      await device.setURLBlacklist([]);

      // Retry login
      await element(by.id('login-button')).tap();

      // Verify successful login
      await waitFor(element(by.id('home-screen')))
        .toBeVisible()
        .withTimeout(10000);
    });

    it('should handle invalid form inputs', async () => {
      // Try to login with invalid email
      await element(by.id('login-email-input')).typeText('invalid-email');
      await element(by.id('login-password-input')).typeText('password123');
      await element(by.id('login-button')).tap();

      // Verify validation error
      await expect(element(by.text('Please enter a valid email address')))
        .toBeVisible();

      // Try with empty password
      await element(by.id('login-email-input')).clearText();
      await element(by.id('login-email-input')).typeText('<EMAIL>');
      await element(by.id('login-password-input')).clearText();
      await element(by.id('login-button')).tap();

      // Verify password validation error
      await expect(element(by.text('Password is required')))
        .toBeVisible();
    });
  });

  describe('Performance Tests', () => {
    it('should load screens within acceptable time limits', async () => {
      // Login
      const loginStart = Date.now();
      await element(by.id('login-email-input')).typeText('<EMAIL>');
      await element(by.id('login-password-input')).typeText('password123');
      await element(by.id('login-button')).tap();

      await waitFor(element(by.id('home-screen')))
        .toBeVisible()
        .withTimeout(5000);
      
      const loginTime = Date.now() - loginStart;
      expect(loginTime).toBeLessThan(5000); // Login should take less than 5 seconds

      // Test navigation performance
      const navStart = Date.now();
      await element(by.id('offers-tab')).tap();
      
      await waitFor(element(by.id('offers-screen')))
        .toBeVisible()
        .withTimeout(3000);
      
      const navTime = Date.now() - navStart;
      expect(navTime).toBeLessThan(1000); // Navigation should take less than 1 second
    });
  });
});
