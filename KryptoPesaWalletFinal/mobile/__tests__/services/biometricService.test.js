import { biometricService } from '../../src/services/biometricService';
import ReactNativeBiometrics from 'react-native-biometrics';
import EncryptedStorage from 'react-native-encrypted-storage';

// Mock dependencies
jest.mock('react-native-biometrics');
jest.mock('react-native-encrypted-storage');
jest.mock('react-native', () => ({
  Alert: {
    alert: jest.fn(),
  },
  Platform: {
    OS: 'ios',
  },
}));

describe('BiometricService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    biometricService.isInitialized = false;
    biometricService.biometricType = null;
  });

  describe('initialize', () => {
    it('should initialize successfully when biometrics are available', async () => {
      ReactNativeBiometrics.mockImplementation(() => ({
        isSensorAvailable: jest.fn().mockResolvedValue({
          available: true,
          biometryType: 'TouchID',
        }),
      }));

      const result = await biometricService.initialize();

      expect(result).toBe(true);
      expect(biometricService.isInitialized).toBe(true);
      expect(biometricService.biometricType).toBe('TouchID');
    });

    it('should fail to initialize when biometrics are not available', async () => {
      ReactNativeBiometrics.mockImplementation(() => ({
        isSensorAvailable: jest.fn().mockResolvedValue({
          available: false,
          biometryType: null,
        }),
      }));

      const result = await biometricService.initialize();

      expect(result).toBe(false);
      expect(biometricService.isInitialized).toBe(false);
    });

    it('should handle initialization errors gracefully', async () => {
      ReactNativeBiometrics.mockImplementation(() => ({
        isSensorAvailable: jest.fn().mockRejectedValue(new Error('Sensor error')),
      }));

      const result = await biometricService.initialize();

      expect(result).toBe(false);
      expect(biometricService.isInitialized).toBe(false);
    });
  });

  describe('createBiometricKey', () => {
    beforeEach(() => {
      ReactNativeBiometrics.mockImplementation(() => ({
        isSensorAvailable: jest.fn().mockResolvedValue({
          available: true,
          biometryType: 'TouchID',
        }),
        biometricKeysExist: jest.fn().mockResolvedValue({ keysExist: false }),
        createKeys: jest.fn().mockResolvedValue({ publicKey: 'mock-public-key' }),
      }));
    });

    it('should create new biometric keys when none exist', async () => {
      EncryptedStorage.setItem.mockResolvedValue();

      const publicKey = await biometricService.createBiometricKey();

      expect(publicKey).toBe('mock-public-key');
      expect(EncryptedStorage.setItem).toHaveBeenCalledWith(
        'biometric_public_key',
        'mock-public-key'
      );
    });

    it('should return existing public key when keys already exist', async () => {
      const mockRnBiometrics = {
        isSensorAvailable: jest.fn().mockResolvedValue({
          available: true,
          biometryType: 'TouchID',
        }),
        biometricKeysExist: jest.fn().mockResolvedValue({ keysExist: true }),
      };
      
      ReactNativeBiometrics.mockImplementation(() => mockRnBiometrics);
      EncryptedStorage.getItem.mockResolvedValue('existing-public-key');

      const publicKey = await biometricService.createBiometricKey();

      expect(publicKey).toBe('existing-public-key');
      expect(mockRnBiometrics.createKeys).not.toHaveBeenCalled();
    });
  });

  describe('enableBiometricAuth', () => {
    beforeEach(() => {
      biometricService.isBiometricAvailable = jest.fn().mockResolvedValue(true);
      biometricService.createBiometricKey = jest.fn().mockResolvedValue('mock-key');
      biometricService.authenticateWithBiometrics = jest.fn().mockResolvedValue({
        success: true,
      });
      EncryptedStorage.setItem.mockResolvedValue();
    });

    it('should enable biometric authentication successfully', async () => {
      const result = await biometricService.enableBiometricAuth('<EMAIL>');

      expect(result).toBe(true);
      expect(biometricService.createBiometricKey).toHaveBeenCalled();
      expect(biometricService.authenticateWithBiometrics).toHaveBeenCalledWith(
        'Enable biometric authentication for KryptoPesa?'
      );
      expect(EncryptedStorage.setItem).toHaveBeenCalledWith('biometric_enabled', 'true');
      expect(EncryptedStorage.setItem).toHaveBeenCalledWith(
        'biometric_user_identifier',
        '<EMAIL>'
      );
    });

    it('should fail when biometrics are not available', async () => {
      biometricService.isBiometricAvailable = jest.fn().mockResolvedValue(false);

      const result = await biometricService.enableBiometricAuth();

      expect(result).toBe(false);
      expect(biometricService.createBiometricKey).not.toHaveBeenCalled();
    });

    it('should fail when authentication test fails', async () => {
      biometricService.authenticateWithBiometrics = jest.fn().mockResolvedValue({
        success: false,
      });

      const result = await biometricService.enableBiometricAuth();

      expect(result).toBe(false);
      expect(EncryptedStorage.setItem).not.toHaveBeenCalledWith('biometric_enabled', 'true');
    });
  });

  describe('authenticateWithBiometrics', () => {
    beforeEach(() => {
      biometricService.isBiometricAvailable = jest.fn().mockResolvedValue(true);
      biometricService.isBiometricEnabled = jest.fn().mockResolvedValue(true);
    });

    it('should authenticate successfully', async () => {
      const mockRnBiometrics = {
        createSignature: jest.fn().mockResolvedValue({
          success: true,
          signature: 'mock-signature',
        }),
      };
      
      ReactNativeBiometrics.mockImplementation(() => mockRnBiometrics);

      const result = await biometricService.authenticateWithBiometrics('Test prompt');

      expect(result.success).toBe(true);
      expect(result.signature).toBe('mock-signature');
      expect(mockRnBiometrics.createSignature).toHaveBeenCalledWith({
        promptMessage: 'Test prompt',
        payload: expect.any(String),
      });
    });

    it('should fail when biometrics are not available', async () => {
      biometricService.isBiometricAvailable = jest.fn().mockResolvedValue(false);

      const result = await biometricService.authenticateWithBiometrics();

      expect(result.success).toBe(false);
      expect(result.error).toBe('Biometric authentication not available');
    });

    it('should fail when biometrics are not enabled', async () => {
      biometricService.isBiometricEnabled = jest.fn().mockResolvedValue(false);

      const result = await biometricService.authenticateWithBiometrics();

      expect(result.success).toBe(false);
      expect(result.error).toBe('Biometric authentication not enabled');
    });

    it('should handle user cancellation', async () => {
      const mockRnBiometrics = {
        createSignature: jest.fn().mockRejectedValue(new Error('UserCancel')),
      };
      
      ReactNativeBiometrics.mockImplementation(() => mockRnBiometrics);

      const result = await biometricService.authenticateWithBiometrics();

      expect(result.success).toBe(false);
      expect(result.error).toBe('Authentication cancelled by user');
    });

    it('should handle biometry not enrolled error', async () => {
      const mockRnBiometrics = {
        createSignature: jest.fn().mockRejectedValue(new Error('BiometryNotEnrolled')),
      };
      
      ReactNativeBiometrics.mockImplementation(() => mockRnBiometrics);

      const result = await biometricService.authenticateWithBiometrics();

      expect(result.success).toBe(false);
      expect(result.error).toBe('No biometric credentials enrolled');
    });
  });

  describe('disableBiometricAuth', () => {
    beforeEach(() => {
      ReactNativeBiometrics.mockImplementation(() => ({
        deleteKeys: jest.fn().mockResolvedValue(),
      }));
      EncryptedStorage.removeItem.mockResolvedValue();
    });

    it('should disable biometric authentication successfully', async () => {
      const result = await biometricService.disableBiometricAuth();

      expect(result).toBe(true);
      expect(EncryptedStorage.removeItem).toHaveBeenCalledWith('biometric_public_key');
      expect(EncryptedStorage.removeItem).toHaveBeenCalledWith('biometric_enabled');
      expect(EncryptedStorage.removeItem).toHaveBeenCalledWith('biometric_user_identifier');
    });

    it('should handle errors gracefully', async () => {
      ReactNativeBiometrics.mockImplementation(() => ({
        deleteKeys: jest.fn().mockRejectedValue(new Error('Delete error')),
      }));

      const result = await biometricService.disableBiometricAuth();

      expect(result).toBe(false);
    });
  });

  describe('isBiometricEnabled', () => {
    it('should return true when biometrics are enabled and keys exist', async () => {
      EncryptedStorage.getItem.mockResolvedValue('true');
      ReactNativeBiometrics.mockImplementation(() => ({
        biometricKeysExist: jest.fn().mockResolvedValue({ keysExist: true }),
      }));

      const result = await biometricService.isBiometricEnabled();

      expect(result).toBe(true);
    });

    it('should return false when biometrics are not enabled', async () => {
      EncryptedStorage.getItem.mockResolvedValue('false');
      ReactNativeBiometrics.mockImplementation(() => ({
        biometricKeysExist: jest.fn().mockResolvedValue({ keysExist: true }),
      }));

      const result = await biometricService.isBiometricEnabled();

      expect(result).toBe(false);
    });

    it('should return false when keys do not exist', async () => {
      EncryptedStorage.getItem.mockResolvedValue('true');
      ReactNativeBiometrics.mockImplementation(() => ({
        biometricKeysExist: jest.fn().mockResolvedValue({ keysExist: false }),
      }));

      const result = await biometricService.isBiometricEnabled();

      expect(result).toBe(false);
    });

    it('should handle errors gracefully', async () => {
      EncryptedStorage.getItem.mockRejectedValue(new Error('Storage error'));

      const result = await biometricService.isBiometricEnabled();

      expect(result).toBe(false);
    });
  });

  describe('getBiometricTypeLabel', () => {
    it('should return correct labels for different biometric types', () => {
      biometricService.biometricType = 'TouchID';
      expect(biometricService.getBiometricTypeLabel()).toBe('Touch ID');

      biometricService.biometricType = 'FaceID';
      expect(biometricService.getBiometricTypeLabel()).toBe('Face ID');

      biometricService.biometricType = 'Biometrics';
      expect(biometricService.getBiometricTypeLabel()).toBe('Fingerprint');

      biometricService.biometricType = null;
      expect(biometricService.getBiometricTypeLabel()).toBe('Biometric Authentication');
    });
  });
});
