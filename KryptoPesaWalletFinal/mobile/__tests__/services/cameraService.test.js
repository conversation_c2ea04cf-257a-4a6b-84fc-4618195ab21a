import { cameraService } from '../../src/services/cameraService';
import { launchCamera, launchImageLibrary } from 'react-native-image-picker';
import { PermissionsAndroid, Alert } from 'react-native';
import RNFS from 'react-native-fs';

// Mock dependencies
jest.mock('react-native-image-picker');
jest.mock('react-native-fs');
jest.mock('react-native', () => ({
  Alert: {
    alert: jest.fn(),
  },
  Platform: {
    OS: 'android',
  },
  PermissionsAndroid: {
    request: jest.fn(),
    PERMISSIONS: {
      CAMERA: 'android.permission.CAMERA',
      WRITE_EXTERNAL_STORAGE: 'android.permission.WRITE_EXTERNAL_STORAGE',
    },
    RESULTS: {
      GRANTED: 'granted',
      DENIED: 'denied',
    },
  },
}));

describe('CameraService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    cameraService.isInitialized = false;
  });

  describe('initialize', () => {
    it('should initialize successfully on Android with permissions', async () => {
      PermissionsAndroid.request
        .mockResolvedValueOnce('granted') // Camera permission
        .mockResolvedValueOnce('granted'); // Storage permission

      const result = await cameraService.initialize();

      expect(result).toBe(true);
      expect(cameraService.isInitialized).toBe(true);
      expect(PermissionsAndroid.request).toHaveBeenCalledTimes(2);
    });

    it('should fail to initialize when permissions are denied', async () => {
      PermissionsAndroid.request
        .mockResolvedValueOnce('denied') // Camera permission
        .mockResolvedValueOnce('granted'); // Storage permission

      const result = await cameraService.initialize();

      expect(result).toBe(false);
    });

    it('should handle permission request errors', async () => {
      PermissionsAndroid.request.mockRejectedValue(new Error('Permission error'));

      const result = await cameraService.initialize();

      expect(result).toBe(false);
    });
  });

  describe('capturePaymentProof', () => {
    beforeEach(() => {
      cameraService.isInitialized = true;
      Alert.alert.mockImplementation((title, message, buttons) => {
        // Simulate user selecting "Take Photo"
        buttons[0].onPress();
      });
    });

    it('should capture payment proof using camera successfully', async () => {
      const mockAsset = {
        uri: 'file://mock-image.jpg',
        type: 'image/jpeg',
        fileSize: 1024000,
        width: 1920,
        height: 1080,
      };

      launchCamera.mockImplementation((options, callback) => {
        callback({
          assets: [mockAsset],
        });
      });

      RNFS.DocumentDirectoryPath = '/mock/documents';
      RNFS.exists.mockResolvedValue(false);
      RNFS.mkdir.mockResolvedValue();
      RNFS.copyFile.mockResolvedValue();
      RNFS.stat.mockResolvedValue({ size: 1024000 });

      const result = await cameraService.capturePaymentProof('trade-123');

      expect(result).toBeDefined();
      expect(result.tradeId).toBe('trade-123');
      expect(result.fileName).toMatch(/payment_proof_trade-123_\d+\.jpg/);
      expect(RNFS.copyFile).toHaveBeenCalled();
    });

    it('should capture payment proof from gallery successfully', async () => {
      Alert.alert.mockImplementation((title, message, buttons) => {
        // Simulate user selecting "Choose from Gallery"
        buttons[1].onPress();
      });

      const mockAsset = {
        uri: 'content://mock-gallery-image.jpg',
        type: 'image/jpeg',
        fileSize: 2048000,
        width: 2048,
        height: 1536,
      };

      launchImageLibrary.mockImplementation((options, callback) => {
        callback({
          assets: [mockAsset],
        });
      });

      RNFS.DocumentDirectoryPath = '/mock/documents';
      RNFS.exists.mockResolvedValue(true);
      RNFS.copyFile.mockResolvedValue();
      RNFS.stat.mockResolvedValue({ size: 2048000 });

      const result = await cameraService.capturePaymentProof('trade-456');

      expect(result).toBeDefined();
      expect(result.tradeId).toBe('trade-456');
      expect(launchImageLibrary).toHaveBeenCalled();
    });

    it('should handle user cancellation', async () => {
      Alert.alert.mockImplementation((title, message, buttons) => {
        // Simulate user selecting "Cancel"
        buttons[2].onPress();
      });

      const result = await cameraService.capturePaymentProof('trade-123');

      expect(result).toBeNull();
    });

    it('should handle camera errors', async () => {
      launchCamera.mockImplementation((options, callback) => {
        callback({
          errorMessage: 'Camera not available',
        });
      });

      await expect(cameraService.capturePaymentProof('trade-123')).rejects.toThrow(
        'Camera not available'
      );
    });

    it('should handle image processing errors', async () => {
      const mockAsset = {
        uri: 'file://mock-image.jpg',
        type: 'image/jpeg',
        fileSize: 1024000,
        width: 1920,
        height: 1080,
      };

      launchCamera.mockImplementation((options, callback) => {
        callback({
          assets: [mockAsset],
        });
      });

      RNFS.copyFile.mockRejectedValue(new Error('File copy failed'));

      await expect(cameraService.capturePaymentProof('trade-123')).rejects.toThrow(
        'File copy failed'
      );
    });
  });

  describe('validateImage', () => {
    it('should validate correct image format and size', () => {
      const validAsset = {
        type: 'image/jpeg',
        fileSize: 5000000, // 5MB
      };

      const result = cameraService.validateImage(validAsset);

      expect(result).toBe(true);
    });

    it('should reject images that are too large', () => {
      const largeAsset = {
        type: 'image/jpeg',
        fileSize: 15000000, // 15MB
      };

      Alert.alert.mockImplementation(() => {});

      const result = cameraService.validateImage(largeAsset);

      expect(result).toBe(false);
      expect(Alert.alert).toHaveBeenCalledWith('Error', 'Image size must be less than 10MB');
    });

    it('should reject unsupported image formats', () => {
      const invalidAsset = {
        type: 'image/gif',
        fileSize: 1000000,
      };

      Alert.alert.mockImplementation(() => {});

      const result = cameraService.validateImage(invalidAsset);

      expect(result).toBe(false);
      expect(Alert.alert).toHaveBeenCalledWith('Error', 'Only JPEG and PNG images are allowed');
    });
  });

  describe('captureMultipleProofs', () => {
    beforeEach(() => {
      cameraService.capturePaymentProof = jest.fn();
    });

    it('should capture multiple payment proofs', async () => {
      Alert.alert
        .mockImplementationOnce((title, message, buttons) => {
          // First prompt - user wants to add image
          buttons[0].onPress();
        })
        .mockImplementationOnce((title, message, buttons) => {
          // Second prompt - user wants to add another image
          buttons[0].onPress();
        })
        .mockImplementationOnce((title, message, buttons) => {
          // Third prompt - user is done
          buttons[1].onPress();
        });

      cameraService.capturePaymentProof
        .mockResolvedValueOnce({ id: 'proof1', tradeId: 'trade-123' })
        .mockResolvedValueOnce({ id: 'proof2', tradeId: 'trade-123' });

      const result = await cameraService.captureMultipleProofs('trade-123', 3);

      expect(result).toHaveLength(2);
      expect(cameraService.capturePaymentProof).toHaveBeenCalledTimes(2);
    });

    it('should handle user canceling on first prompt', async () => {
      Alert.alert.mockImplementationOnce((title, message, buttons) => {
        // User cancels on first prompt
        buttons[1].onPress();
      });

      const result = await cameraService.captureMultipleProofs('trade-123');

      expect(result).toHaveLength(0);
      expect(cameraService.capturePaymentProof).not.toHaveBeenCalled();
    });
  });

  describe('deletePaymentProof', () => {
    it('should delete payment proof file successfully', async () => {
      const proof = {
        localPath: '/mock/path/proof.jpg',
      };

      RNFS.exists.mockResolvedValue(true);
      RNFS.unlink.mockResolvedValue();

      const result = await cameraService.deletePaymentProof(proof);

      expect(result).toBe(true);
      expect(RNFS.unlink).toHaveBeenCalledWith('/mock/path/proof.jpg');
    });

    it('should handle file deletion errors', async () => {
      const proof = {
        localPath: '/mock/path/proof.jpg',
      };

      RNFS.exists.mockResolvedValue(true);
      RNFS.unlink.mockRejectedValue(new Error('Delete failed'));

      const result = await cameraService.deletePaymentProof(proof);

      expect(result).toBe(false);
    });

    it('should handle non-existent files gracefully', async () => {
      const proof = {
        localPath: '/mock/path/nonexistent.jpg',
      };

      RNFS.exists.mockResolvedValue(false);

      const result = await cameraService.deletePaymentProof(proof);

      expect(result).toBe(true);
      expect(RNFS.unlink).not.toHaveBeenCalled();
    });
  });

  describe('getPaymentProofs', () => {
    it('should retrieve payment proofs for a trade', async () => {
      RNFS.DocumentDirectoryPath = '/mock/documents';
      RNFS.exists.mockResolvedValue(true);
      RNFS.readDir.mockResolvedValue([
        {
          name: 'payment_proof_trade-123_1640995200000.jpg',
          path: '/mock/documents/payment_proofs/payment_proof_trade-123_1640995200000.jpg',
          size: 1024000,
        },
        {
          name: 'payment_proof_trade-456_1640995300000.jpg',
          path: '/mock/documents/payment_proofs/payment_proof_trade-456_1640995300000.jpg',
          size: 2048000,
        },
      ]);

      const result = await cameraService.getPaymentProofs('trade-123');

      expect(result).toHaveLength(1);
      expect(result[0].tradeId).toBe('trade-123');
      expect(result[0].timestamp).toBe(1640995200000);
    });

    it('should return empty array when directory does not exist', async () => {
      RNFS.exists.mockResolvedValue(false);

      const result = await cameraService.getPaymentProofs('trade-123');

      expect(result).toEqual([]);
    });

    it('should handle read directory errors', async () => {
      RNFS.exists.mockResolvedValue(true);
      RNFS.readDir.mockRejectedValue(new Error('Read error'));

      const result = await cameraService.getPaymentProofs('trade-123');

      expect(result).toEqual([]);
    });
  });

  describe('clearAllPaymentProofs', () => {
    it('should clear all payment proofs successfully', async () => {
      RNFS.DocumentDirectoryPath = '/mock/documents';
      RNFS.exists.mockResolvedValue(true);
      RNFS.unlink.mockResolvedValue();

      const result = await cameraService.clearAllPaymentProofs();

      expect(result).toBe(true);
      expect(RNFS.unlink).toHaveBeenCalledWith('/mock/documents/payment_proofs');
    });

    it('should handle non-existent directory gracefully', async () => {
      RNFS.exists.mockResolvedValue(false);

      const result = await cameraService.clearAllPaymentProofs();

      expect(result).toBe(true);
      expect(RNFS.unlink).not.toHaveBeenCalled();
    });

    it('should handle deletion errors', async () => {
      RNFS.exists.mockResolvedValue(true);
      RNFS.unlink.mockRejectedValue(new Error('Delete failed'));

      const result = await cameraService.clearAllPaymentProofs();

      expect(result).toBe(false);
    });
  });
});
