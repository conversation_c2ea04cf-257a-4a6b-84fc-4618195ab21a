const express = require('express');
const mongoose = require('mongoose');
const { body, validationResult, query } = require('express-validator');
const { verifyToken } = require('./auth');
const User = require('../models/User');
const Trade = require('../models/Trade');
const Offer = require('../models/Offer');
const Dispute = require('../models/Dispute');
const Chat = require('../models/Chat');
const { AppError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');

const router = express.Router();

// Middleware to verify admin/moderator role
const verifyAdminRole = async (req, res, next) => {
  try {
    if (!req.user || !['admin', 'moderator'].includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        message: 'Access denied. Admin privileges required.'
      });
    }
    next();
  } catch (error) {
    next(error);
  }
};

// Apply authentication and admin verification to all routes
router.use(verifyToken);
router.use(verifyAdminRole);

// Dashboard statistics
router.get('/dashboard/stats', async (req, res, next) => {
  try {
    const [
      totalUsers,
      activeUsers,
      totalTrades,
      activeTrades,
      completedTrades,
      pendingDisputes,
      totalOffers,
      activeOffers
    ] = await Promise.all([
      User.countDocuments(),
      User.countDocuments({ lastActive: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) } }),
      Trade.countDocuments(),
      Trade.countDocuments({ status: { $in: ['created', 'funded', 'payment_sent'] } }),
      Trade.countDocuments({ status: 'completed' }),
      Dispute.countDocuments({ status: { $in: ['open', 'investigating'] } }),
      Offer.countDocuments(),
      Offer.countDocuments({ status: 'active' })
    ]);

    // Calculate volume statistics
    const volumeStats = await Trade.aggregate([
      { $match: { status: 'completed' } },
      {
        $group: {
          _id: null,
          totalVolume: { $sum: { $toDouble: '$cryptocurrency.amount' } },
          totalFiatVolume: { $sum: '$fiat.amount' }
        }
      }
    ]);

    // Recent activity
    const recentTrades = await Trade.find()
      .sort({ createdAt: -1 })
      .limit(5)
      .populate('seller buyer', 'username profile.firstName profile.lastName')
      .select('tradeId cryptocurrency fiat status createdAt');

    const recentDisputes = await Dispute.find()
      .sort({ createdAt: -1 })
      .limit(5)
      .populate('trade initiator', 'tradeId username')
      .select('disputeId reason status priority createdAt');

    res.json({
      success: true,
      data: {
        statistics: {
          users: {
            total: totalUsers,
            active: activeUsers,
            growth: 0 // TODO: Calculate growth percentage
          },
          trades: {
            total: totalTrades,
            active: activeTrades,
            completed: completedTrades,
            volume: volumeStats[0]?.totalVolume || 0,
            fiatVolume: volumeStats[0]?.totalFiatVolume || 0
          },
          disputes: {
            pending: pendingDisputes,
            total: await Dispute.countDocuments()
          },
          offers: {
            total: totalOffers,
            active: activeOffers
          }
        },
        recentActivity: {
          trades: recentTrades,
          disputes: recentDisputes
        }
      }
    });

  } catch (error) {
    next(error);
  }
});

// User Management Routes
router.get('/users', [
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  query('search').optional().isLength({ min: 1 }).withMessage('Search term cannot be empty'),
  query('status').optional().isIn(['active', 'suspended', 'banned', 'pending']).withMessage('Invalid status'),
  query('role').optional().isIn(['user', 'admin', 'moderator']).withMessage('Invalid role'),
  query('verified').optional().isBoolean().withMessage('Verified must be boolean'),
  query('sortBy').optional().isIn(['createdAt', 'lastActive', 'reputation.score', 'username']).withMessage('Invalid sort field'),
  query('sortOrder').optional().isIn(['asc', 'desc']).withMessage('Sort order must be asc or desc')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const {
      page = 1,
      limit = 20,
      search,
      status,
      role,
      verified,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    // Build query
    const query = {};

    if (search) {
      query.$or = [
        { username: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
        { 'profile.firstName': { $regex: search, $options: 'i' } },
        { 'profile.lastName': { $regex: search, $options: 'i' } }
      ];
    }

    if (status) query.status = status;
    if (role) query.role = role;
    if (verified !== undefined) query['verification.identity.verified'] = verified === 'true';

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const sortOptions = { [sortBy]: sortOrder === 'desc' ? -1 : 1 };

    const [users, totalUsers] = await Promise.all([
      User.find(query)
        .select('-password -security.twoFactorSecret')
        .sort(sortOptions)
        .skip(skip)
        .limit(parseInt(limit))
        .lean(),
      User.countDocuments(query)
    ]);

    res.json({
      success: true,
      data: {
        users,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(totalUsers / parseInt(limit)),
          totalUsers,
          hasNext: skip + parseInt(limit) < totalUsers,
          hasPrev: parseInt(page) > 1
        }
      }
    });

  } catch (error) {
    next(error);
  }
});

// Get specific user details
router.get('/users/:userId', async (req, res, next) => {
  try {
    const { userId } = req.params;

    if (!mongoose.Types.ObjectId.isValid(userId)) {
      throw new AppError('Invalid user ID', 400);
    }

    const user = await User.findById(userId)
      .select('-password -security.twoFactorSecret')
      .lean();

    if (!user) {
      throw new AppError('User not found', 404);
    }

    // Get user's trade statistics
    const tradeStats = await Trade.aggregate([
      {
        $match: {
          $or: [{ seller: new mongoose.Types.ObjectId(userId) }, { buyer: new mongoose.Types.ObjectId(userId) }]
        }
      },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
          totalVolume: { $sum: { $toDouble: '$cryptocurrency.amount' } }
        }
      }
    ]);

    // Get recent trades
    const recentTrades = await Trade.find({
      $or: [{ seller: userId }, { buyer: userId }]
    })
      .sort({ createdAt: -1 })
      .limit(10)
      .populate('seller buyer', 'username')
      .select('tradeId cryptocurrency fiat status createdAt');

    // Get disputes initiated by user
    const disputes = await Dispute.find({ initiator: userId })
      .sort({ createdAt: -1 })
      .limit(5)
      .populate('trade', 'tradeId')
      .select('disputeId reason status createdAt');

    res.json({
      success: true,
      data: {
        user,
        statistics: {
          trades: tradeStats,
          disputes: disputes.length
        },
        recentActivity: {
          trades: recentTrades,
          disputes
        }
      }
    });

  } catch (error) {
    next(error);
  }
});

// Update user status
router.put('/users/:userId/status', [
  body('status').isIn(['active', 'suspended', 'banned', 'pending']).withMessage('Invalid status'),
  body('reason').optional().isLength({ min: 1, max: 500 }).withMessage('Reason must be 1-500 characters'),
  body('duration').optional().isInt({ min: 1 }).withMessage('Duration must be positive integer (hours)')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { userId } = req.params;
    const { status, reason, duration } = req.body;

    if (!mongoose.Types.ObjectId.isValid(userId)) {
      throw new AppError('Invalid user ID', 400);
    }

    const user = await User.findById(userId);
    if (!user) {
      throw new AppError('User not found', 404);
    }

    // Prevent modifying admin/moderator accounts unless you're an admin
    if (['admin', 'moderator'].includes(user.role) && req.user.role !== 'admin') {
      throw new AppError('Insufficient privileges to modify this account', 403);
    }

    const updateData = { status };

    // Add suspension/ban details if applicable
    if (['suspended', 'banned'].includes(status)) {
      updateData['moderation.status'] = status;
      updateData['moderation.reason'] = reason;
      updateData['moderation.moderatedBy'] = req.user._id;
      updateData['moderation.moderatedAt'] = new Date();

      if (duration && status === 'suspended') {
        updateData['moderation.suspendedUntil'] = new Date(Date.now() + duration * 60 * 60 * 1000);
      }
    }

    const updatedUser = await User.findByIdAndUpdate(
      userId,
      updateData,
      { new: true, select: '-password -security.twoFactorSecret' }
    );

    // Log the action
    logger.info(`Admin ${req.user.username} changed user ${user.username} status to ${status}`, {
      adminId: req.user._id,
      userId,
      oldStatus: user.status,
      newStatus: status,
      reason
    });

    res.json({
      success: true,
      message: `User status updated to ${status}`,
      data: { user: updatedUser }
    });

  } catch (error) {
    next(error);
  }
});

// Verify user identity
router.put('/users/:userId/verify', [
  body('documentType').isIn(['national_id', 'passport', 'driving_license']).withMessage('Invalid document type'),
  body('approved').isBoolean().withMessage('Approved must be boolean'),
  body('notes').optional().isLength({ max: 500 }).withMessage('Notes cannot exceed 500 characters')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { userId } = req.params;
    const { documentType, approved, notes } = req.body;

    if (!mongoose.Types.ObjectId.isValid(userId)) {
      throw new AppError('Invalid user ID', 400);
    }

    const updateData = {
      'verification.identity.verified': approved,
      'verification.identity.verifiedAt': approved ? new Date() : null,
      'verification.identity.verifiedBy': approved ? req.user._id : null,
      'verification.identity.documentType': documentType
    };

    if (notes) {
      updateData['verification.identity.notes'] = notes;
    }

    const user = await User.findByIdAndUpdate(
      userId,
      updateData,
      { new: true, select: '-password -security.twoFactorSecret' }
    );

    if (!user) {
      throw new AppError('User not found', 404);
    }

    logger.info(`Admin ${req.user.username} ${approved ? 'approved' : 'rejected'} identity verification for user ${user.username}`, {
      adminId: req.user._id,
      userId,
      documentType,
      approved,
      notes
    });

    res.json({
      success: true,
      message: `Identity verification ${approved ? 'approved' : 'rejected'}`,
      data: { user }
    });

  } catch (error) {
    next(error);
  }
});

// Trade Management Routes
router.get('/trades', [
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  query('status').optional().isIn(['created', 'funded', 'payment_sent', 'payment_confirmed', 'completed', 'disputed', 'cancelled', 'refunded', 'expired']).withMessage('Invalid status'),
  query('cryptocurrency').optional().isIn(['USDT', 'USDC', 'DAI', 'BTC', 'ETH']).withMessage('Invalid cryptocurrency'),
  query('fiatCurrency').optional().isIn(['KES', 'TZS', 'UGX', 'RWF', 'USD']).withMessage('Invalid fiat currency'),
  query('sortBy').optional().isIn(['createdAt', 'fiat.amount', 'cryptocurrency.amount']).withMessage('Invalid sort field'),
  query('sortOrder').optional().isIn(['asc', 'desc']).withMessage('Sort order must be asc or desc')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const {
      page = 1,
      limit = 20,
      status,
      cryptocurrency,
      fiatCurrency,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    // Build query
    const query = {};
    if (status) query.status = status;
    if (cryptocurrency) query['cryptocurrency.symbol'] = cryptocurrency;
    if (fiatCurrency) query['fiat.currency'] = fiatCurrency;

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const sortOptions = { [sortBy]: sortOrder === 'desc' ? -1 : 1 };

    const [trades, totalTrades] = await Promise.all([
      Trade.find(query)
        .populate('seller buyer', 'username profile.firstName profile.lastName reputation.score')
        .populate('offer', 'offerId type')
        .sort(sortOptions)
        .skip(skip)
        .limit(parseInt(limit))
        .lean(),
      Trade.countDocuments(query)
    ]);

    res.json({
      success: true,
      data: {
        trades,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(totalTrades / parseInt(limit)),
          totalTrades,
          hasNext: skip + parseInt(limit) < totalTrades,
          hasPrev: parseInt(page) > 1
        }
      }
    });

  } catch (error) {
    next(error);
  }
});

// Get specific trade details
router.get('/trades/:tradeId', async (req, res, next) => {
  try {
    const { tradeId } = req.params;

    const trade = await Trade.findOne({ tradeId })
      .populate('seller buyer', 'username profile email phone reputation verification')
      .populate('offer', 'offerId type terms')
      .populate('chat')
      .populate('dispute')
      .lean();

    if (!trade) {
      throw new AppError('Trade not found', 404);
    }

    // Get chat messages for context
    const chat = await Chat.findById(trade.chat)
      .populate('messages.sender', 'username')
      .select('messages')
      .lean();

    res.json({
      success: true,
      data: {
        trade,
        chat: chat?.messages || []
      }
    });

  } catch (error) {
    next(error);
  }
});

// Intervene in trade (admin action)
router.post('/trades/:tradeId/intervene', [
  body('action').isIn(['cancel', 'force_complete', 'extend_deadline', 'add_note']).withMessage('Invalid action'),
  body('reason').isLength({ min: 1, max: 500 }).withMessage('Reason is required and must be 1-500 characters'),
  body('extensionHours').optional().isInt({ min: 1, max: 168 }).withMessage('Extension must be 1-168 hours')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { tradeId } = req.params;
    const { action, reason, extensionHours } = req.body;

    const trade = await Trade.findOne({ tradeId });
    if (!trade) {
      throw new AppError('Trade not found', 404);
    }

    let updateData = {};
    let message = '';

    switch (action) {
      case 'cancel':
        updateData.status = 'cancelled';
        updateData.cancelReason = `Admin intervention: ${reason}`;
        updateData.cancelledAt = new Date();
        message = 'Trade cancelled by admin';
        break;

      case 'force_complete':
        updateData.status = 'completed';
        updateData.completedAt = new Date();
        updateData['confirmations.seller.paymentReceived'] = true;
        updateData['confirmations.buyer.paymentSent'] = true;
        message = 'Trade force completed by admin';
        break;

      case 'extend_deadline':
        updateData.expiresAt = new Date(trade.expiresAt.getTime() + extensionHours * 60 * 60 * 1000);
        message = `Trade deadline extended by ${extensionHours} hours`;
        break;

      case 'add_note':
        // Add to timeline instead of updating status
        break;
    }

    // Add timeline entry
    const timelineEntry = {
      status: action === 'add_note' ? 'admin_note' : action,
      timestamp: new Date(),
      actor: req.user._id,
      note: reason
    };

    if (action === 'add_note') {
      await Trade.findOneAndUpdate(
        { tradeId },
        { $push: { timeline: timelineEntry } }
      );
    } else {
      await Trade.findOneAndUpdate(
        { tradeId },
        {
          ...updateData,
          $push: { timeline: timelineEntry }
        }
      );
    }

    // Log the intervention
    logger.warn(`Admin intervention on trade ${tradeId}`, {
      adminId: req.user._id,
      tradeId,
      action,
      reason
    });

    res.json({
      success: true,
      message: message || 'Note added to trade',
      data: { action, reason }
    });

  } catch (error) {
    next(error);
  }
});

// Dispute Management Routes
router.get('/disputes', [
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  query('status').optional().isIn(['open', 'investigating', 'resolved', 'closed']).withMessage('Invalid status'),
  query('priority').optional().isIn(['low', 'medium', 'high', 'urgent']).withMessage('Invalid priority'),
  query('assignedTo').optional().isMongoId().withMessage('Invalid assignedTo ID'),
  query('sortBy').optional().isIn(['createdAt', 'priority', 'status']).withMessage('Invalid sort field'),
  query('sortOrder').optional().isIn(['asc', 'desc']).withMessage('Sort order must be asc or desc')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const {
      page = 1,
      limit = 20,
      status,
      priority,
      assignedTo,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    // Build query
    const query = {};
    if (status) query.status = status;
    if (priority) query.priority = priority;
    if (assignedTo) query.assignedTo = assignedTo;

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const sortOptions = { [sortBy]: sortOrder === 'desc' ? -1 : 1 };

    const [disputes, totalDisputes] = await Promise.all([
      Dispute.find(query)
        .populate('trade', 'tradeId cryptocurrency fiat seller buyer')
        .populate('initiator', 'username profile.firstName profile.lastName')
        .populate('assignedTo', 'username profile.firstName profile.lastName')
        .populate('resolution.resolvedBy', 'username')
        .sort(sortOptions)
        .skip(skip)
        .limit(parseInt(limit))
        .lean(),
      Dispute.countDocuments(query)
    ]);

    res.json({
      success: true,
      data: {
        disputes,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(totalDisputes / parseInt(limit)),
          totalDisputes,
          hasNext: skip + parseInt(limit) < totalDisputes,
          hasPrev: parseInt(page) > 1
        }
      }
    });

  } catch (error) {
    next(error);
  }
});

// Get specific dispute details
router.get('/disputes/:disputeId', async (req, res, next) => {
  try {
    const { disputeId } = req.params;

    const dispute = await Dispute.findOne({ disputeId })
      .populate('trade')
      .populate('initiator', 'username profile email phone reputation')
      .populate('assignedTo', 'username profile')
      .populate('resolution.resolvedBy', 'username profile')
      .lean();

    if (!dispute) {
      throw new AppError('Dispute not found', 404);
    }

    // Get full trade details
    const trade = await Trade.findById(dispute.trade)
      .populate('seller buyer', 'username profile email phone reputation')
      .populate('chat')
      .lean();

    // Get chat messages for context
    const chat = await Chat.findById(trade.chat)
      .populate('messages.sender', 'username')
      .select('messages')
      .lean();

    res.json({
      success: true,
      data: {
        dispute,
        trade,
        chat: chat?.messages || []
      }
    });

  } catch (error) {
    next(error);
  }
});

// Assign dispute to admin/moderator
router.put('/disputes/:disputeId/assign', [
  body('assignedTo').isMongoId().withMessage('Invalid assignedTo ID'),
  body('notes').optional().isLength({ max: 500 }).withMessage('Notes cannot exceed 500 characters')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { disputeId } = req.params;
    const { assignedTo, notes } = req.body;

    // Verify assignee is admin/moderator
    const assignee = await User.findById(assignedTo);
    if (!assignee || !['admin', 'moderator'].includes(assignee.role)) {
      throw new AppError('Can only assign to admin or moderator', 400);
    }

    const updateData = {
      assignedTo,
      status: 'investigating',
      'investigation.assignedAt': new Date(),
      'investigation.assignedBy': req.user._id
    };

    if (notes) {
      updateData['investigation.notes'] = notes;
    }

    const dispute = await Dispute.findOneAndUpdate(
      { disputeId },
      updateData,
      { new: true }
    ).populate('assignedTo', 'username');

    if (!dispute) {
      throw new AppError('Dispute not found', 404);
    }

    logger.info(`Dispute ${disputeId} assigned to ${assignee.username}`, {
      adminId: req.user._id,
      disputeId,
      assignedTo,
      notes
    });

    res.json({
      success: true,
      message: `Dispute assigned to ${assignee.username}`,
      data: { dispute }
    });

  } catch (error) {
    next(error);
  }
});

// Resolve dispute
router.post('/disputes/:disputeId/resolve', [
  body('decision').isIn(['favor_buyer', 'favor_seller', 'partial_refund', 'no_action']).withMessage('Invalid decision'),
  body('reasoning').isLength({ min: 10, max: 1000 }).withMessage('Reasoning must be 10-1000 characters'),
  body('actionTaken').isIn(['release_escrow', 'refund_escrow', 'partial_release', 'manual_intervention']).withMessage('Invalid action'),
  body('refundPercentage').optional().isFloat({ min: 0, max: 100 }).withMessage('Refund percentage must be 0-100')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { disputeId } = req.params;
    const { decision, reasoning, actionTaken, refundPercentage } = req.body;

    const dispute = await Dispute.findOne({ disputeId }).populate('trade');
    if (!dispute) {
      throw new AppError('Dispute not found', 404);
    }

    if (dispute.status === 'resolved') {
      throw new AppError('Dispute already resolved', 400);
    }

    // Update dispute with resolution
    const resolutionData = {
      status: 'resolved',
      'resolution.decision': decision,
      'resolution.reasoning': reasoning,
      'resolution.actionTaken': actionTaken,
      'resolution.resolvedBy': req.user._id,
      'resolution.resolvedAt': new Date()
    };

    if (refundPercentage !== undefined) {
      resolutionData['resolution.refundPercentage'] = refundPercentage;
    }

    const resolvedDispute = await Dispute.findOneAndUpdate(
      { disputeId },
      resolutionData,
      { new: true }
    );

    // Update trade status based on resolution
    let tradeUpdateData = {};
    switch (actionTaken) {
      case 'release_escrow':
        tradeUpdateData.status = 'completed';
        tradeUpdateData.completedAt = new Date();
        break;
      case 'refund_escrow':
        tradeUpdateData.status = 'refunded';
        break;
      case 'partial_release':
        tradeUpdateData.status = 'completed';
        tradeUpdateData.completedAt = new Date();
        break;
    }

    if (Object.keys(tradeUpdateData).length > 0) {
      await Trade.findByIdAndUpdate(dispute.trade._id, tradeUpdateData);
    }

    logger.info(`Dispute ${disputeId} resolved`, {
      adminId: req.user._id,
      disputeId,
      decision,
      actionTaken,
      reasoning: reasoning.substring(0, 100)
    });

    res.json({
      success: true,
      message: 'Dispute resolved successfully',
      data: { dispute: resolvedDispute }
    });

  } catch (error) {
    next(error);
  }
});

module.exports = router;