const express = require('express');
const mongoose = require('mongoose');
const redis = require('redis');
const os = require('os');
const { promisify } = require('util');

const router = express.Router();

// Create Redis client for health checks
let redisClient;
try {
  redisClient = redis.createClient({
    host: process.env.REDIS_HOST || 'localhost',
    port: process.env.REDIS_PORT || 6379,
    password: process.env.REDIS_PASSWORD,
  });
} catch (error) {
  console.error('Redis client creation failed:', error);
}

// Health check endpoint
router.get('/', async (req, res) => {
  const healthCheck = {
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development',
    version: process.env.npm_package_version || '1.0.0',
    services: {},
    system: {},
    performance: {}
  };

  try {
    // Check MongoDB connection
    healthCheck.services.mongodb = await checkMongoDB();
    
    // Check Redis connection
    healthCheck.services.redis = await checkRedis();
    
    // System information
    healthCheck.system = getSystemInfo();
    
    // Performance metrics
    healthCheck.performance = getPerformanceMetrics();
    
    // Determine overall status
    const allServicesHealthy = Object.values(healthCheck.services).every(
      service => service.status === 'healthy'
    );
    
    if (!allServicesHealthy) {
      healthCheck.status = 'DEGRADED';
      res.status(503);
    }
    
    res.json(healthCheck);
  } catch (error) {
    healthCheck.status = 'ERROR';
    healthCheck.error = error.message;
    res.status(500).json(healthCheck);
  }
});

// Detailed health check endpoint
router.get('/detailed', async (req, res) => {
  try {
    const detailedHealth = {
      status: 'OK',
      timestamp: new Date().toISOString(),
      checks: {
        database: await checkDatabaseHealth(),
        cache: await checkCacheHealth(),
        external: await checkExternalServices(),
        application: await checkApplicationHealth()
      },
      metrics: await getDetailedMetrics()
    };

    // Determine overall status
    const failedChecks = Object.values(detailedHealth.checks).filter(
      check => check.status !== 'healthy'
    );

    if (failedChecks.length > 0) {
      detailedHealth.status = failedChecks.some(check => check.status === 'critical') 
        ? 'CRITICAL' 
        : 'DEGRADED';
      res.status(503);
    }

    res.json(detailedHealth);
  } catch (error) {
    res.status(500).json({
      status: 'ERROR',
      timestamp: new Date().toISOString(),
      error: error.message
    });
  }
});

// Readiness probe
router.get('/ready', async (req, res) => {
  try {
    // Check if all critical services are ready
    const mongoReady = mongoose.connection.readyState === 1;
    const redisReady = redisClient ? await checkRedisConnection() : true;

    if (mongoReady && redisReady) {
      res.status(200).json({ status: 'ready' });
    } else {
      res.status(503).json({ 
        status: 'not ready',
        mongodb: mongoReady,
        redis: redisReady
      });
    }
  } catch (error) {
    res.status(503).json({ 
      status: 'not ready',
      error: error.message 
    });
  }
});

// Liveness probe
router.get('/live', (req, res) => {
  res.status(200).json({ 
    status: 'alive',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

// Helper functions
async function checkMongoDB() {
  try {
    const state = mongoose.connection.readyState;
    const stateMap = {
      0: 'disconnected',
      1: 'connected',
      2: 'connecting',
      3: 'disconnecting'
    };

    if (state === 1) {
      // Test with a simple query
      await mongoose.connection.db.admin().ping();
      return {
        status: 'healthy',
        state: stateMap[state],
        responseTime: await measureMongoResponseTime()
      };
    } else {
      return {
        status: 'unhealthy',
        state: stateMap[state],
        error: 'MongoDB not connected'
      };
    }
  } catch (error) {
    return {
      status: 'unhealthy',
      error: error.message
    };
  }
}

async function checkRedis() {
  if (!redisClient) {
    return {
      status: 'disabled',
      message: 'Redis client not configured'
    };
  }

  try {
    const start = Date.now();
    await redisClient.ping();
    const responseTime = Date.now() - start;

    return {
      status: 'healthy',
      responseTime: `${responseTime}ms`
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      error: error.message
    };
  }
}

async function checkRedisConnection() {
  if (!redisClient) return true;
  
  try {
    await redisClient.ping();
    return true;
  } catch (error) {
    return false;
  }
}

function getSystemInfo() {
  return {
    platform: os.platform(),
    arch: os.arch(),
    nodeVersion: process.version,
    totalMemory: `${Math.round(os.totalmem() / 1024 / 1024)} MB`,
    freeMemory: `${Math.round(os.freemem() / 1024 / 1024)} MB`,
    loadAverage: os.loadavg(),
    cpuCount: os.cpus().length
  };
}

function getPerformanceMetrics() {
  const memUsage = process.memoryUsage();
  return {
    memory: {
      rss: `${Math.round(memUsage.rss / 1024 / 1024)} MB`,
      heapTotal: `${Math.round(memUsage.heapTotal / 1024 / 1024)} MB`,
      heapUsed: `${Math.round(memUsage.heapUsed / 1024 / 1024)} MB`,
      external: `${Math.round(memUsage.external / 1024 / 1024)} MB`
    },
    uptime: `${Math.round(process.uptime())} seconds`,
    pid: process.pid
  };
}

async function measureMongoResponseTime() {
  const start = Date.now();
  await mongoose.connection.db.admin().ping();
  return `${Date.now() - start}ms`;
}

async function checkDatabaseHealth() {
  try {
    const User = require('../models/User');
    const Trade = require('../models/Trade');
    
    const [userCount, tradeCount] = await Promise.all([
      User.countDocuments(),
      Trade.countDocuments()
    ]);

    return {
      status: 'healthy',
      collections: {
        users: userCount,
        trades: tradeCount
      },
      responseTime: await measureMongoResponseTime()
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      error: error.message
    };
  }
}

async function checkCacheHealth() {
  if (!redisClient) {
    return { status: 'disabled' };
  }

  try {
    const start = Date.now();
    await redisClient.set('health_check', 'test', 'EX', 10);
    const value = await redisClient.get('health_check');
    const responseTime = Date.now() - start;

    if (value === 'test') {
      return {
        status: 'healthy',
        responseTime: `${responseTime}ms`
      };
    } else {
      return {
        status: 'unhealthy',
        error: 'Cache read/write test failed'
      };
    }
  } catch (error) {
    return {
      status: 'unhealthy',
      error: error.message
    };
  }
}

async function checkExternalServices() {
  // Add checks for external services like blockchain nodes, payment APIs, etc.
  return {
    status: 'healthy',
    services: {
      // blockchain: await checkBlockchainConnection(),
      // notifications: await checkNotificationService()
    }
  };
}

async function checkApplicationHealth() {
  try {
    // Check if critical application components are working
    const checks = {
      routes: true, // Could test critical routes
      middleware: true, // Could test middleware functionality
      services: true // Could test service layer
    };

    return {
      status: 'healthy',
      components: checks
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      error: error.message
    };
  }
}

async function getDetailedMetrics() {
  const memUsage = process.memoryUsage();
  const cpuUsage = process.cpuUsage();
  
  return {
    process: {
      pid: process.pid,
      uptime: process.uptime(),
      memory: {
        rss: memUsage.rss,
        heapTotal: memUsage.heapTotal,
        heapUsed: memUsage.heapUsed,
        external: memUsage.external,
        arrayBuffers: memUsage.arrayBuffers
      },
      cpu: {
        user: cpuUsage.user,
        system: cpuUsage.system
      }
    },
    system: {
      loadAverage: os.loadavg(),
      totalMemory: os.totalmem(),
      freeMemory: os.freemem(),
      uptime: os.uptime()
    }
  };
}

module.exports = router;
