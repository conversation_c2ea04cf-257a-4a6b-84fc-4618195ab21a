const express = require('express');
const { body, validationResult } = require('express-validator');
const { verifyToken } = require('./auth');
const walletService = require('../services/walletService');
const { ethers } = require('ethers');
const logger = require('../utils/logger');

const router = express.Router();

// All wallet routes require authentication
router.use(verifyToken);

// Create new wallet
router.post('/create', async (req, res, next) => {
  try {
    const result = await walletService.createWallet(req.user._id);

    res.status(201).json({
      success: true,
      message: 'Wallet created successfully',
      data: {
        addresses: result.addresses,
        mnemonic: result.mnemonic // Only returned once during creation
      }
    });

  } catch (error) {
    next(error);
  }
});

// Import existing wallet
router.post('/import', [
  body('mnemonic')
    .notEmpty()
    .withMessage('Mnemonic phrase is required')
    .custom((value) => {
      if (!ethers.utils.isValidMnemonic(value)) {
        throw new Error('Invalid mnemonic phrase');
      }
      return true;
    })
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { mnemonic } = req.body;
    const result = await walletService.importWallet(req.user._id, mnemonic);

    res.status(201).json({
      success: true,
      message: 'Wallet imported successfully',
      data: {
        addresses: result.addresses
      }
    });

  } catch (error) {
    next(error);
  }
});

// Get wallet info
router.get('/', async (req, res, next) => {
  try {
    const wallet = await walletService.getWallet(req.user._id);

    // Remove sensitive data
    const walletData = wallet.toObject();
    delete walletData.security.encryptedMnemonic;
    delete walletData.security.mnemonicHash;

    res.json({
      success: true,
      data: { wallet: walletData }
    });

  } catch (error) {
    next(error);
  }
});

// Update balances
router.post('/balances/refresh', async (req, res, next) => {
  try {
    const wallet = await walletService.updateBalances(req.user._id);

    res.json({
      success: true,
      message: 'Balances updated successfully',
      data: { balances: wallet.balances }
    });

  } catch (error) {
    next(error);
  }
});

// Get transaction history
router.get('/transactions', async (req, res, next) => {
  try {
    const limit = parseInt(req.query.limit) || 50;
    const offset = parseInt(req.query.offset) || 0;

    const result = await walletService.getTransactionHistory(req.user._id, limit, offset);

    res.json({
      success: true,
      data: result
    });

  } catch (error) {
    next(error);
  }
});

// Verify mnemonic
router.post('/verify-mnemonic', [
  body('mnemonic')
    .notEmpty()
    .withMessage('Mnemonic phrase is required')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { mnemonic } = req.body;
    const isValid = await walletService.verifyMnemonic(req.user._id, mnemonic);

    res.json({
      success: true,
      data: { isValid }
    });

  } catch (error) {
    next(error);
  }
});

// Mark backup as completed
router.post('/backup/complete', async (req, res, next) => {
  try {
    await walletService.markBackupCompleted(req.user._id);

    res.json({
      success: true,
      message: 'Backup marked as completed'
    });

  } catch (error) {
    next(error);
  }
});

// Get wallet statistics
router.get('/stats', async (req, res, next) => {
  try {
    const stats = await walletService.getWalletStats(req.user._id);

    res.json({
      success: true,
      data: { stats }
    });

  } catch (error) {
    next(error);
  }
});

module.exports = router;