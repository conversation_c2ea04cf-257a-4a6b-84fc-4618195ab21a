const rateLimit = require('express-rate-limit');
const RedisStore = require('rate-limit-redis');
const { getRedisClient } = require('../config/redis');
const logger = require('../utils/logger');

// Rate limit configurations for different endpoint types
const RATE_LIMITS = {
  // Authentication endpoints - very strict
  auth: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 5, // 5 attempts per window
    skipSuccessfulRequests: true,
    skipFailedRequests: false,
  },
  
  // Trading endpoints - moderate
  trading: {
    windowMs: 1 * 60 * 1000, // 1 minute
    max: 10, // 10 trades per minute
    skipSuccessfulRequests: false,
    skipFailedRequests: true,
  },
  
  // Wallet operations - strict
  wallet: {
    windowMs: 5 * 60 * 1000, // 5 minutes
    max: 20, // 20 operations per 5 minutes
    skipSuccessfulRequests: false,
    skipFailedRequests: true,
  },
  
  // Chat/messaging - moderate
  chat: {
    windowMs: 1 * 60 * 1000, // 1 minute
    max: 30, // 30 messages per minute
    skipSuccessfulRequests: false,
    skipFailedRequests: true,
  },
  
  // General API - lenient
  general: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // 100 requests per 15 minutes
    skipSuccessfulRequests: false,
    skipFailedRequests: true,
  },
  
  // Public endpoints - very lenient
  public: {
    windowMs: 1 * 60 * 1000, // 1 minute
    max: 60, // 60 requests per minute
    skipSuccessfulRequests: false,
    skipFailedRequests: true,
  },
};

// Create Redis store for distributed rate limiting
const createRedisStore = () => {
  const redisClient = getRedisClient();
  if (redisClient && redisClient.isReady) {
    return new RedisStore({
      client: redisClient,
      prefix: 'rl:',
    });
  }
  return undefined; // Fall back to memory store
};

// Enhanced rate limiter with custom logic
const createAdvancedRateLimiter = (config, type) => {
  return rateLimit({
    ...config,
    store: createRedisStore(),
    standardHeaders: true,
    legacyHeaders: false,
    
    // Custom key generator to include user ID for authenticated requests
    keyGenerator: (req) => {
      const baseKey = req.ip;
      const userKey = req.user ? req.user._id.toString() : 'anonymous';
      return `${type}:${baseKey}:${userKey}`;
    },
    
    // Custom message with retry information
    message: (req, res) => {
      const retryAfter = Math.round(config.windowMs / 1000);
      return {
        success: false,
        error: 'Rate limit exceeded',
        message: `Too many ${type} requests. Please try again in ${retryAfter} seconds.`,
        retryAfter,
        limit: config.max,
        windowMs: config.windowMs,
      };
    },
    
    // Custom handler for rate limit exceeded
    handler: (req, res, next, options) => {
      logger.warn(`Rate limit exceeded for ${type}`, {
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        endpoint: req.originalUrl,
        userId: req.user ? req.user._id : null,
      });
      
      res.status(429).json(options.message(req, res));
    },
    
    // Skip rate limiting for certain conditions
    skip: (req) => {
      // Skip for health checks
      if (req.path === '/health') return true;
      
      // Skip for admin users in development
      if (process.env.NODE_ENV === 'development' && req.user?.role === 'admin') {
        return true;
      }
      
      return false;
    },
  });
};

// Specific rate limiters
const authLimiter = createAdvancedRateLimiter(RATE_LIMITS.auth, 'auth');
const tradingLimiter = createAdvancedRateLimiter(RATE_LIMITS.trading, 'trading');
const walletLimiter = createAdvancedRateLimiter(RATE_LIMITS.wallet, 'wallet');
const chatLimiter = createAdvancedRateLimiter(RATE_LIMITS.chat, 'chat');
const generalLimiter = createAdvancedRateLimiter(RATE_LIMITS.general, 'general');
const publicLimiter = createAdvancedRateLimiter(RATE_LIMITS.public, 'public');

// Strict limiter for sensitive operations
const strictLimiter = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: 3, // Only 3 attempts per minute
  store: createRedisStore(),
  standardHeaders: true,
  legacyHeaders: false,
  message: {
    success: false,
    error: 'Rate limit exceeded',
    message: 'Too many sensitive operations. Please wait before trying again.',
    retryAfter: 60,
  },
  handler: (req, res, next, options) => {
    logger.error('Strict rate limit exceeded - potential attack', {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      endpoint: req.originalUrl,
      userId: req.user ? req.user._id : null,
    });
    
    res.status(429).json(options.message);
  },
});

// Progressive rate limiter that increases restrictions based on violations
const progressiveLimiter = (req, res, next) => {
  const redisClient = getRedisClient();
  if (!redisClient || !redisClient.isReady) {
    return next();
  }
  
  const key = `progressive:${req.ip}`;
  
  redisClient.get(key)
    .then(violations => {
      const violationCount = parseInt(violations) || 0;
      
      // Increase restrictions based on violation count
      let maxRequests = 100;
      let windowMs = 15 * 60 * 1000;
      
      if (violationCount > 0) {
        maxRequests = Math.max(10, 100 - (violationCount * 20));
        windowMs = Math.min(60 * 60 * 1000, 15 * 60 * 1000 + (violationCount * 5 * 60 * 1000));
      }
      
      // Apply dynamic rate limit
      const dynamicLimiter = rateLimit({
        windowMs,
        max: maxRequests,
        store: createRedisStore(),
        keyGenerator: (req) => `dynamic:${req.ip}`,
        handler: (req, res, next, options) => {
          // Increment violation count
          redisClient.incr(key);
          redisClient.expire(key, 24 * 60 * 60); // Expire after 24 hours
          
          logger.warn('Progressive rate limit exceeded', {
            ip: req.ip,
            violations: violationCount + 1,
            maxRequests,
            windowMs,
          });
          
          res.status(429).json({
            success: false,
            error: 'Rate limit exceeded',
            message: 'Request rate too high. Restrictions have been increased.',
            retryAfter: Math.round(windowMs / 1000),
          });
        },
      });
      
      dynamicLimiter(req, res, next);
    })
    .catch(error => {
      logger.error('Progressive rate limiter error:', error);
      next(); // Continue without rate limiting on Redis error
    });
};

// Body size limiter based on endpoint type
const createBodySizeLimiter = (maxSize) => {
  return (req, res, next) => {
    const contentLength = parseInt(req.get('Content-Length')) || 0;
    
    if (contentLength > maxSize) {
      logger.warn('Request body too large', {
        ip: req.ip,
        contentLength,
        maxSize,
        endpoint: req.originalUrl,
      });
      
      return res.status(413).json({
        success: false,
        error: 'Payload too large',
        message: `Request body must be smaller than ${Math.round(maxSize / 1024 / 1024)}MB`,
        maxSize,
      });
    }
    
    next();
  };
};

// Export rate limiters
module.exports = {
  authLimiter,
  tradingLimiter,
  walletLimiter,
  chatLimiter,
  generalLimiter,
  publicLimiter,
  strictLimiter,
  progressiveLimiter,
  createBodySizeLimiter,
  RATE_LIMITS,
};
