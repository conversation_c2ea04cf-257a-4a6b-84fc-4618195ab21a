{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mEnvironment warning: REDIS_URL appears to be using a default/weak value\u001b[39m","timestamp":"2025-07-03 16:56:18:5618"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mEnvironment validation completed successfully\u001b[39m","timestamp":"2025-07-03 16:56:18:5618"}
{"hasAWS":false,"hasBlockchain":true,"hasDatabase":true,"hasFirebase":false,"hasMonitoring":false,"hasRedis":true,"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mEnvironment configuration:\u001b[39m","nodeEnv":"development","port":"3000","timestamp":"2025-07-03 16:56:18:5618"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mKryptoPesa API server running on port 3000\u001b[39m","timestamp":"2025-07-03 16:56:18:5618"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mEnvironment: development\u001b[39m","timestamp":"2025-07-03 16:56:18:5618"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mDatabase connection failed: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\u001b[39m","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"},"stack":"MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (/Users/<USER>/KryptoPesaWalletFinal/backend/node_modules/mongoose/lib/connection.js:816:11)\n    at NativeConnection.openUri (/Users/<USER>/KryptoPesaWalletFinal/backend/node_modules/mongoose/lib/connection.js:791:11)\n    at async connectDB (/Users/<USER>/KryptoPesaWalletFinal/backend/src/config/database.js:15:18)","timestamp":"2025-07-03 16:56:23:5623"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mEnvironment warning: REDIS_URL appears to be using a default/weak value\u001b[39m","timestamp":"2025-07-03 16:56:48:5648"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mEnvironment validation completed successfully\u001b[39m","timestamp":"2025-07-03 16:56:48:5648"}
{"hasAWS":false,"hasBlockchain":true,"hasDatabase":true,"hasFirebase":false,"hasMonitoring":false,"hasRedis":true,"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mEnvironment configuration:\u001b[39m","nodeEnv":"development","port":"3000","timestamp":"2025-07-03 16:56:48:5648"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mKryptoPesa API server running on port 3000\u001b[39m","timestamp":"2025-07-03 16:56:48:5648"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mEnvironment: development\u001b[39m","timestamp":"2025-07-03 16:56:48:5648"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [03/Jul/2025:13:57:18 +0000] \"GET /health HTTP/1.1\" 200 104 \"-\" \"curl/8.7.1\"\u001b[39m","timestamp":"2025-07-03 16:57:18:5718"}
