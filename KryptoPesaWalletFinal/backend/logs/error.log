{"level": "\u001b[31<PERSON><PERSON>r\u001b[39m", "message": "\u001b[31mDatabase connection failed: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\u001b[39m", "reason": {"commonWireVersion": 0, "compatible": true, "heartbeatFrequencyMS": 10000, "localThresholdMS": 15, "logicalSessionTimeoutMinutes": null, "maxElectionId": null, "maxSetVersion": null, "servers": {}, "setName": null, "stale": false, "type": "Unknown"}, "stack": "MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (/Users/<USER>/KryptoPesaWalletFinal/backend/node_modules/mongoose/lib/connection.js:816:11)\n    at NativeConnection.openUri (/Users/<USER>/KryptoPesaWalletFinal/backend/node_modules/mongoose/lib/connection.js:791:11)\n    at async connectDB (/Users/<USER>/KryptoPesaWalletFinal/backend/src/config/database.js:15:18)", "timestamp": "2025-07-03 16:56:23:5623"}